#!/bin/bash

echo "🚀 启动 Wood Caving 开发环境..."

# 检查 Node.js 版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 20 ]; then
    echo "❌ 需要 Node.js 20+，当前版本: $(node --version)"
    echo "请运行: nvm install 20 && nvm use 20"
    exit 1
fi

# 检查必要的命令
command -v yarn >/dev/null 2>&1 || { echo "❌ 需要安装 yarn: npm install -g yarn"; exit 1; }
command -v pnpm >/dev/null 2>&1 || { echo "❌ 需要安装 pnpm: npm install -g pnpm"; exit 1; }

# 启动数据库服务
echo "📊 启动数据库服务..."
brew services start postgresql@15 2>/dev/null || echo "PostgreSQL 可能已在运行"
brew services start redis 2>/dev/null || echo "Redis 可能已在运行"

# 等待服务启动
sleep 2

# 设置数据库
echo "🗄️ 设置数据库..."
./setup-database.sh

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
yarn install

# 运行数据库迁移和种子数据
echo "🌱 初始化数据库..."
yarn build
yarn seed

# 启动后端
echo "🔧 启动后端服务..."
yarn dev &
BACKEND_PID=$!

# 等待后端启动
echo "⏳ 等待后端启动..."
sleep 10

# 安装前端依赖
echo "📦 安装前端依赖..."
cd ../storefront
pnpm install

# 启动前端
echo "🎨 启动前端服务..."
pnpm dev &
FRONTEND_PID=$!

echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📍 服务地址:"
echo "   前端: http://localhost:3000"
echo "   后端 API: http://localhost:9000"
echo "   管理后台: http://localhost:9000/app"
echo ""
echo "⏹️  停止服务: Ctrl+C 然后运行 ./stop-dev.sh"

# 保存进程 ID
echo $BACKEND_PID > .backend.pid
echo $FRONTEND_PID > .frontend.pid

# 等待用户中断
wait
