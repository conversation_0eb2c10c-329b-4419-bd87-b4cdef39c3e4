#!/bin/bash

# Wood Carving 项目数据库恢复脚本
# 用于在新设备上快速初始化数据库

set -e

# 配置
DB_NAME="medusa_dev"
BACKUP_DIR="./database-backups"

# 设置 PostgreSQL 路径
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

echo "🔄 Wood Carving 数据库恢复工具"
echo "================================"

# 检查参数
BACKUP_FILE="$1"
if [ -z "$BACKUP_FILE" ]; then
    # 如果没有指定文件，使用最新备份
    BACKUP_FILE="$BACKUP_DIR/latest_backup.sql.gz"
    if [ ! -f "$BACKUP_FILE" ]; then
        echo "❌ 没有找到备份文件"
        echo ""
        echo "使用方法:"
        echo "  $0 [备份文件路径]"
        echo ""
        echo "示例:"
        echo "  $0 ./database-backups/medusa_backup_20241215_143022.sql.gz"
        echo "  $0  # 使用最新备份"
        echo ""
        echo "可用的备份文件:"
        if [ -d "$BACKUP_DIR" ]; then
            ls -la "$BACKUP_DIR"/*.sql.gz 2>/dev/null || echo "  (没有找到备份文件)"
        else
            echo "  (备份目录不存在)"
        fi
        exit 1
    fi
    echo "📁 使用最新备份: $BACKUP_FILE"
else
    if [ ! -f "$BACKUP_FILE" ]; then
        echo "❌ 备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
    echo "📁 使用指定备份: $BACKUP_FILE"
fi

echo ""
echo "📊 恢复信息:"
echo "  目标数据库: $DB_NAME"
echo "  备份文件: $BACKUP_FILE"
echo "  文件大小: $(du -h "$BACKUP_FILE" | cut -f1)"
echo ""

# 确认操作
read -p "⚠️  这将删除现有数据库并恢复备份数据。确认继续? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 1
fi

# 检查 PostgreSQL 服务
echo "🔍 检查 PostgreSQL 服务..."
if ! pg_isready > /dev/null 2>&1; then
    echo "❌ PostgreSQL 服务未运行"
    echo "请先启动 PostgreSQL 服务:"
    echo "  brew services start postgresql@15"
    exit 1
fi

echo "✅ PostgreSQL 服务正常"

# 停止可能连接到数据库的进程
echo ""
echo "🛑 停止相关服务..."
echo "请确保已停止以下服务:"
echo "  - Medusa 后端服务 (yarn dev)"
echo "  - 前端服务 (npm run dev)"
echo ""
read -p "已停止相关服务? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 请先停止相关服务"
    exit 1
fi

# 终止数据库连接
echo "🔌 终止现有数据库连接..."
psql -d postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();
" > /dev/null 2>&1 || true

# 删除现有数据库
echo "🗑️ 删除现有数据库..."
dropdb --if-exists "$DB_NAME" || true

# 创建新数据库
echo "🆕 创建新数据库..."
createdb "$DB_NAME"

# 解压并恢复数据库
echo "📥 恢复数据库数据..."
if [[ "$BACKUP_FILE" == *.gz ]]; then
    echo "  解压并恢复压缩备份..."
    gunzip -c "$BACKUP_FILE" | psql -d "$DB_NAME" -v ON_ERROR_STOP=1
else
    echo "  恢复未压缩备份..."
    psql -d "$DB_NAME" -v ON_ERROR_STOP=1 -f "$BACKUP_FILE"
fi

if [ $? -eq 0 ]; then
    echo "✅ 数据库恢复成功!"
    
    # 验证恢复结果
    echo ""
    echo "🔍 验证恢复结果..."
    echo "数据库表统计:"
    psql -d "$DB_NAME" -c "
    SELECT 
        schemaname,
        tablename,
        n_live_tup as rows
    FROM pg_stat_user_tables 
    WHERE n_live_tup > 0
    ORDER BY n_live_tup DESC
    LIMIT 10;
    "
    
    echo ""
    echo "🎉 数据库恢复完成!"
    echo ""
    echo "📋 下一步操作:"
    echo "  1. 启动 Medusa 后端服务:"
    echo "     cd backend && yarn dev"
    echo ""
    echo "  2. 启动前端服务:"
    echo "     cd storefront && npm run dev"
    echo ""
    echo "  3. 访问应用:"
    echo "     前端: http://localhost:3000/us"
    echo "     管理后台: http://localhost:9000/app"
    echo "     CMS: http://localhost:3000/cms"
    
else
    echo "❌ 数据库恢复失败!"
    exit 1
fi
