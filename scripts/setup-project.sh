#!/bin/bash

# Wood Carving 项目完整初始化脚本
# 用于在新设备上快速设置整个项目

set -e

echo "🎨 Wood Carving 项目初始化工具"
echo "============================="
echo ""

# 检查必要的工具
echo "🔍 检查系统依赖..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    echo "请安装 Node.js 20.x: https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本过低 (当前: $(node -v), 需要: 18+)"
    exit 1
fi

echo "✅ Node.js $(node -v)"

# 检查 PostgreSQL
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL 未安装"
    echo "请安装 PostgreSQL:"
    echo "  macOS: brew install postgresql@15"
    echo "  Ubuntu: sudo apt install postgresql-15"
    exit 1
fi

echo "✅ PostgreSQL $(psql --version | cut -d' ' -f3)"

# 检查 Git
if ! command -v git &> /dev/null; then
    echo "❌ Git 未安装"
    exit 1
fi

echo "✅ Git $(git --version | cut -d' ' -f3)"

# 检查 yarn
if ! command -v yarn &> /dev/null; then
    echo "📦 安装 Yarn..."
    npm install -g yarn
fi

echo "✅ Yarn $(yarn --version)"

echo ""
echo "🚀 开始项目初始化..."

# 1. 安装依赖
echo ""
echo "📦 安装项目依赖..."

echo "  安装根目录依赖..."
npm install

echo "  安装后端依赖..."
cd backend
yarn install
cd ..

echo "  安装前端依赖..."
cd storefront
npm install
cd ..

echo "✅ 依赖安装完成"

# 2. 设置环境变量
echo ""
echo "⚙️ 设置环境变量..."

# 检查后端环境变量
if [ ! -f "backend/.env" ]; then
    echo "❌ 后端环境变量文件不存在: backend/.env"
    echo "请创建 backend/.env 文件，参考 backend/.env.example"
    exit 1
fi

# 检查前端环境变量
if [ ! -f "storefront/.env" ]; then
    echo "❌ 前端环境变量文件不存在: storefront/.env"
    echo "请创建 storefront/.env 文件，参考 storefront/.env.example"
    exit 1
fi

echo "✅ 环境变量文件存在"

# 3. 设置数据库
echo ""
echo "🗄️ 设置数据库..."

# 设置 PostgreSQL 路径
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 检查 PostgreSQL 服务
if ! pg_isready > /dev/null 2>&1; then
    echo "🔄 启动 PostgreSQL 服务..."
    if command -v brew &> /dev/null; then
        brew services start postgresql@15
        sleep 3
    else
        echo "❌ 请手动启动 PostgreSQL 服务"
        exit 1
    fi
fi

# 检查是否有备份文件
BACKUP_DIR="./database-backups"
if [ -f "$BACKUP_DIR/latest_backup.sql.gz" ] || [ -n "$(ls -A $BACKUP_DIR/*.sql.gz 2>/dev/null)" ]; then
    echo "📁 发现数据库备份文件"
    read -p "是否恢复数据库备份? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        ./scripts/restore-database.sh
    else
        echo "跳过数据库恢复，将创建新的数据库..."
        # 创建新数据库并运行迁移
        createdb medusa_dev 2>/dev/null || true
        cd backend
        yarn build
        yarn medusa db:migrate
        cd ..
        
        # 创建初始数据
        echo "🎨 创建初始数据..."
        node create-publishable-key.js
        node create-sanity-initial-data.js
        node create-20-enhanced-products.js
        node create-regions-countries.js
    fi
else
    echo "📝 创建新数据库..."
    createdb medusa_dev 2>/dev/null || true
    cd backend
    yarn build
    yarn medusa db:migrate
    cd ..
    
    # 创建初始数据
    echo "🎨 创建初始数据..."
    node create-publishable-key.js
    node create-sanity-initial-data.js
    node create-20-enhanced-products.js
    node create-regions-countries.js
fi

echo "✅ 数据库设置完成"

# 4. 构建项目
echo ""
echo "🔨 构建项目..."

echo "  构建后端..."
cd backend
yarn build
cd ..

echo "  构建前端..."
cd storefront
npm run build
cd ..

echo "✅ 项目构建完成"

# 5. 创建启动脚本
echo ""
echo "📝 创建启动脚本..."

cat > start-backend.sh << 'EOF'
#!/bin/bash
echo "🚀 启动 Medusa 后端服务..."
cd backend
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 20 2>/dev/null || true
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
yarn dev
EOF

cat > start-frontend.sh << 'EOF'
#!/bin/bash
echo "🚀 启动前端服务..."
cd storefront
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 20 2>/dev/null || true
npm run dev
EOF

chmod +x start-backend.sh start-frontend.sh

echo "✅ 启动脚本创建完成"

# 完成
echo ""
echo "🎉 项目初始化完成!"
echo ""
echo "📋 使用说明:"
echo ""
echo "1. 启动后端服务:"
echo "   ./start-backend.sh"
echo "   或者: cd backend && yarn dev"
echo ""
echo "2. 启动前端服务 (新终端):"
echo "   ./start-frontend.sh"
echo "   或者: cd storefront && npm run dev"
echo ""
echo "3. 访问应用:"
echo "   🌐 前端: http://localhost:3000/us"
echo "   ⚙️  管理后台: http://localhost:9000/app"
echo "   📝 CMS: http://localhost:3000/cms"
echo ""
echo "4. 管理后台登录:"
echo "   📧 邮箱: <EMAIL>"
echo "   🔑 密码: admin123"
echo ""
echo "5. 数据库管理:"
echo "   💾 备份: ./scripts/backup-database.sh"
echo "   🔄 恢复: ./scripts/restore-database.sh"
echo ""
echo "🎯 项目已准备就绪!"
