#!/bin/bash

# Wood Carving 项目数据库备份脚本
# 用于备份当前的 Medusa 数据库，以便在其他设备上快速初始化

set -e

# 配置
DB_NAME="medusa_dev"
BACKUP_DIR="./database-backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/medusa_backup_$TIMESTAMP.sql"

# 设置 PostgreSQL 路径
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

echo "🗄️ Wood Carving 数据库备份工具"
echo "================================"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

echo "📊 备份数据库信息:"
echo "  数据库名: $DB_NAME"
echo "  备份文件: $BACKUP_FILE"
echo "  时间戳: $TIMESTAMP"
echo ""

# 检查数据库连接
echo "🔍 检查数据库连接..."
if ! psql -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
    echo "❌ 无法连接到数据库 $DB_NAME"
    echo "请确保:"
    echo "  1. PostgreSQL 服务正在运行"
    echo "  2. 数据库 $DB_NAME 存在"
    echo "  3. 当前用户有访问权限"
    exit 1
fi

echo "✅ 数据库连接正常"

# 获取数据库统计信息
echo ""
echo "📈 数据库统计信息:"
psql -d "$DB_NAME" -c "
SELECT
    schemaname,
    relname as tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows
FROM pg_stat_user_tables
WHERE n_live_tup > 0
ORDER BY n_live_tup DESC
LIMIT 10;
"

# 执行备份
echo ""
echo "💾 开始备份数据库..."
pg_dump \
    --host=localhost \
    --port=5432 \
    --username="$USER" \
    --dbname="$DB_NAME" \
    --verbose \
    --clean \
    --if-exists \
    --create \
    --format=plain \
    --encoding=UTF8 \
    --no-owner \
    --no-privileges \
    --file="$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "✅ 数据库备份成功!"
    
    # 压缩备份文件
    echo "🗜️ 压缩备份文件..."
    gzip "$BACKUP_FILE"
    COMPRESSED_FILE="${BACKUP_FILE}.gz"
    
    # 显示文件信息
    echo ""
    echo "📁 备份文件信息:"
    echo "  原始文件: $BACKUP_FILE"
    echo "  压缩文件: $COMPRESSED_FILE"
    echo "  文件大小: $(du -h "$COMPRESSED_FILE" | cut -f1)"
    
    # 创建最新备份的软链接
    LATEST_BACKUP="$BACKUP_DIR/latest_backup.sql.gz"
    ln -sf "$(basename "$COMPRESSED_FILE")" "$LATEST_BACKUP"
    echo "  最新备份: $LATEST_BACKUP"
    
    echo ""
    echo "🎉 备份完成!"
    echo ""
    echo "📋 使用说明:"
    echo "  在其他设备上恢复数据库:"
    echo "  1. 复制备份文件到目标设备"
    echo "  2. 运行: ./scripts/restore-database.sh $COMPRESSED_FILE"
    echo ""
    echo "  或者使用最新备份:"
    echo "  ./scripts/restore-database.sh"
    
else
    echo "❌ 数据库备份失败!"
    exit 1
fi

# 清理旧备份（保留最近5个）
echo ""
echo "🧹 清理旧备份文件..."
cd "$BACKUP_DIR"
ls -t medusa_backup_*.sql.gz | tail -n +6 | xargs -r rm -f
echo "✅ 已清理旧备份，保留最近5个备份文件"

echo ""
echo "🎯 备份完成! 文件位置: $COMPRESSED_FILE"
