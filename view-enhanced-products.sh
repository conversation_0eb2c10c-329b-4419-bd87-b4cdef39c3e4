#!/bin/bash

echo "🎨 Enhanced Wood Carving Products 数据查看工具"
echo "=============================================="

# 设置 PostgreSQL 路径
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

show_menu() {
    echo ""
    echo "请选择查看内容："
    echo "1. 查看所有增强字段的产品信息"
    echo "2. 查看产品分类层级结构"
    echo "3. 查看设计师作品统计"
    echo "4. 查看材料使用统计"
    echo "5. 查看新品和畅销品"
    echo "6. 查看价格区间分布"
    echo "7. 查看产品完整详情（包含所有新字段）"
    echo "8. 查看 SKU 编码信息"
    echo "9. 退出"
    echo ""
}

show_enhanced_products() {
    echo "🎨 增强字段产品信息"
    echo "=================="
    psql -d medusa_dev -c "
        SELECT 
            chinese_name as \"中文商品名称\",
            designer_name as \"设计师名称\",
            material as \"材料\",
            size_inches as \"产品尺寸(英寸)\",
            weight_grams as \"重量(克)\",
            CASE WHEN is_new_product THEN '是' ELSE '否' END as \"是否新品\",
            CASE WHEN is_bestseller THEN '是' ELSE '否' END as \"是否畅销品\",
            '¥' || purchase_price_cny as \"采购价格(人民币)\",
            '$' || sale_price_usd as \"销售价格(美金)\"
        FROM product_full_info 
        WHERE chinese_name IS NOT NULL
        ORDER BY created_at DESC 
        LIMIT 10;
    "
}

show_category_hierarchy() {
    echo "📁 产品分类层级结构"
    echo "=================="
    psql -d medusa_dev -c "
        SELECT 
            CASE 
                WHEN level = 1 THEN '📂 ' || name
                WHEN level = 2 THEN '  📁 ' || name  
                WHEN level = 3 THEN '    📄 ' || name
            END as \"分类结构\",
            level as \"层级\",
            path as \"完整路径\"
        FROM product_category_hierarchy 
        ORDER BY path;
    "
}

show_designer_stats() {
    echo "👨‍🎨 设计师作品统计"
    echo "=================="
    psql -d medusa_dev -c "
        SELECT 
            designer_name as \"设计师名称\",
            COUNT(*) as \"作品数量\",
            COUNT(CASE WHEN is_new_product THEN 1 END) as \"新品数量\",
            COUNT(CASE WHEN is_bestseller THEN 1 END) as \"畅销品数量\",
            ROUND(AVG(purchase_price_cny), 2) as \"平均采购价格\",
            ROUND(AVG(sale_price_usd), 2) as \"平均销售价格\"
        FROM product 
        WHERE designer_name IS NOT NULL AND deleted_at IS NULL
        GROUP BY designer_name 
        ORDER BY COUNT(*) DESC;
    "
}

show_material_stats() {
    echo "🪵 材料使用统计"
    echo "=============="
    psql -d medusa_dev -c "
        SELECT 
            material as \"材料类型\",
            COUNT(*) as \"使用次数\",
            ROUND(AVG(weight_grams), 0) as \"平均重量(克)\",
            ROUND(AVG(purchase_price_cny), 2) as \"平均采购价格\",
            ROUND(AVG(sale_price_usd), 2) as \"平均销售价格\"
        FROM product 
        WHERE material IS NOT NULL AND deleted_at IS NULL
        GROUP BY material 
        ORDER BY COUNT(*) DESC;
    "
}

show_new_bestseller() {
    echo "🆕🔥 新品和畅销品"
    echo "================"
    echo ""
    echo "🆕 新品列表："
    psql -d medusa_dev -c "
        SELECT 
            title as \"产品名称\",
            designer_name as \"设计师\",
            material as \"材料\",
            '¥' || purchase_price_cny as \"采购价格\",
            '$' || sale_price_usd as \"销售价格\"
        FROM product 
        WHERE is_new_product = true AND deleted_at IS NULL
        ORDER BY created_at DESC;
    "
    
    echo ""
    echo "🔥 畅销品列表："
    psql -d medusa_dev -c "
        SELECT 
            title as \"产品名称\",
            designer_name as \"设计师\",
            material as \"材料\",
            '¥' || purchase_price_cny as \"采购价格\",
            '$' || sale_price_usd as \"销售价格\"
        FROM product 
        WHERE is_bestseller = true AND deleted_at IS NULL
        ORDER BY sale_price_usd DESC;
    "
}

show_price_distribution() {
    echo "💰 价格区间分布"
    echo "=============="
    psql -d medusa_dev -c "
        SELECT 
            CASE 
                WHEN purchase_price_cny < 500 THEN '低价位 (<¥500)'
                WHEN purchase_price_cny < 1000 THEN '中低价位 (¥500-999)'
                WHEN purchase_price_cny < 1500 THEN '中价位 (¥1000-1499)'
                WHEN purchase_price_cny < 2000 THEN '中高价位 (¥1500-1999)'
                ELSE '高价位 (≥¥2000)'
            END as \"价格区间\",
            COUNT(*) as \"产品数量\",
            ROUND(AVG(purchase_price_cny), 2) as \"平均采购价格\",
            ROUND(AVG(sale_price_usd), 2) as \"平均销售价格\"
        FROM product 
        WHERE purchase_price_cny IS NOT NULL AND deleted_at IS NULL
        GROUP BY 
            CASE 
                WHEN purchase_price_cny < 500 THEN 1
                WHEN purchase_price_cny < 1000 THEN 2
                WHEN purchase_price_cny < 1500 THEN 3
                WHEN purchase_price_cny < 2000 THEN 4
                ELSE 5
            END,
            CASE 
                WHEN purchase_price_cny < 500 THEN '低价位 (<¥500)'
                WHEN purchase_price_cny < 1000 THEN '中低价位 (¥500-999)'
                WHEN purchase_price_cny < 1500 THEN '中价位 (¥1000-1499)'
                WHEN purchase_price_cny < 2000 THEN '中高价位 (¥1500-1999)'
                ELSE '高价位 (≥¥2000)'
            END
        ORDER BY 1;
    "
}

show_full_details() {
    echo "📋 产品完整详情（包含所有新字段）"
    echo "=============================="
    psql -d medusa_dev -c "
        SELECT 
            title as \"中文商品名称\",
            designer_name as \"设计师名称\",
            material as \"材料\",
            size_inches as \"产品尺寸(英寸)\",
            weight_grams as \"重量(克)\",
            design_concept as \"设计理念\",
            usage_scenario as \"使用场景\",
            craftsmanship as \"手艺和技能\",
            delivery_scope as \"订单配送范围\",
            CASE WHEN is_new_product THEN '是' ELSE '否' END as \"是否新品\",
            CASE WHEN is_bestseller THEN '是' ELSE '否' END as \"是否畅销品\",
            '¥' || purchase_price_cny as \"采购价格(人民币)\",
            '$' || sale_price_usd as \"销售价格(美金)\"
        FROM product 
        WHERE deleted_at IS NULL AND title IS NOT NULL
        ORDER BY created_at DESC 
        LIMIT 3;
    " | head -50
}

show_sku_info() {
    echo "🏷️ SKU 编码信息"
    echo "=============="
    psql -d medusa_dev -c "
        SELECT 
            p.title as \"产品名称\",
            pv.sku as \"SKU编码\",
            p.material as \"材料\",
            pv.weight as \"重量(kg)\",
            pv.length || '×' || pv.width || '×' || pv.height as \"尺寸(cm)\",
            p.designer_name as \"设计师\"
        FROM product p
        JOIN product_variant pv ON p.id = pv.product_id
        WHERE p.deleted_at IS NULL AND pv.deleted_at IS NULL
        ORDER BY p.created_at DESC
        LIMIT 10;
    "
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (1-9): " choice
    
    case $choice in
        1) show_enhanced_products ;;
        2) show_category_hierarchy ;;
        3) show_designer_stats ;;
        4) show_material_stats ;;
        5) show_new_bestseller ;;
        6) show_price_distribution ;;
        7) show_full_details ;;
        8) show_sku_info ;;
        9) echo "👋 再见！"; exit 0 ;;
        *) echo "❌ 无效选项，请重新选择" ;;
    esac
    
    echo ""
    read -p "按 Enter 继续..."
done
