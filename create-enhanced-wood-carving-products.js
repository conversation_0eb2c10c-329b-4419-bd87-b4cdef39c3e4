const { Client } = require('pg');

// 数据库连接配置
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'medusa_dev',
  user: process.env.USER || 'lilsnake',
});

// 生成唯一ID的函数
function generateId(prefix = '') {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}${random}`;
}

// 随机选择函数
function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// 随机价格生成
function randomPrice(min, max) {
  return (Math.random() * (max - min) + min).toFixed(2);
}

// 木雕产品完整数据
const enhancedWoodCarvingData = {
  // 三级分类结构
  categories: [
    // 一级分类
    { id: generateId('pcat'), name: '传统木雕', handle: 'traditional-carving', level: 1, parent: null },
    { id: generateId('pcat'), name: '现代木艺', handle: 'modern-woodcraft', level: 1, parent: null },
    { id: generateId('pcat'), name: '实用木器', handle: 'functional-wood', level: 1, parent: null },
    
    // 二级分类会在脚本中动态创建
  ],

  // 设计师信息
  designers: [
    '张大师', '李工匠', '王艺人', '陈雕师', '刘木匠',
    '赵师傅', '孙艺术家', '周大师', '吴工艺师', '郑雕刻家'
  ],

  // 材料类型
  materials: [
    '红木', '檀木', '黄花梨', '紫檀', '楠木', 
    '椴木', '乌木', '花梨木', '酸枝木', '鸡翅木'
  ],

  // 设计理念
  designConcepts: [
    '传承千年工艺，融合现代美学，展现东方文化的深厚底蕴',
    '以自然为师，用匠心雕琢，诠释生活中的诗意与美好',
    '古法新韵，精工细作，每一刀都承载着工匠的情怀',
    '天人合一的哲学思想，通过木雕艺术传达内心的宁静',
    '简约而不简单，在朴素中见真章，体现禅意生活',
    '传统技艺与创新设计的完美结合，打造独特的艺术品',
    '师法自然，取材天然，展现木材本身的纹理之美',
    '匠心独运，精雕细琢，每件作品都是独一无二的艺术品'
  ],

  // 使用场景
  usageScenarios: [
    '客厅装饰、书房摆设、茶室点缀',
    '办公室装饰、会议室摆件、接待区展示',
    '卧室装饰、床头柜摆件、私人收藏',
    '餐厅装饰、茶台摆设、家庭聚会',
    '书房收藏、文人雅士、学者书斋',
    '礼品赠送、商务礼品、节日礼物',
    '艺术收藏、投资收藏、传家之宝',
    '禅修空间、冥想室、静心场所'
  ],

  // 手艺技能
  craftsmanshipSkills: [
    '传统手工雕刻、精细打磨、天然漆艺',
    '立体圆雕技法、镂空雕刻、表面抛光',
    '浮雕工艺、阴刻技法、手工上色',
    '榫卯结构、无胶拼接、传统工艺',
    '精雕细琢、层次分明、立体感强',
    '刀法娴熟、线条流畅、造型生动',
    '古法制作、纯手工艺、匠心独运',
    '精工细作、注重细节、完美呈现'
  ],

  // 配送范围
  deliveryScopes: [
    '全国包邮，3-7个工作日送达，支持货到付款',
    '国内顺丰快递，48小时内发货，包装精美',
    '全球配送，海外7-15个工作日，专业包装',
    '同城当日达，异地次日达，保价运输',
    '专业物流，全程跟踪，安全送达',
    '包邮配送，专业包装，破损包赔',
    '快递配送，安全可靠，签收确认',
    '物流配送，全程保险，放心购买'
  ],

  // 产品数据模板
  productTemplates: [
    {
      name: '威武神龙雕刻',
      category: 'traditional-carving',
      subCategory: 'mythical-creatures',
      thirdCategory: 'dragon-series',
      sizeInches: '12×8×6',
      weightGrams: 2500,
      isNew: true,
      isBestseller: false
    },
    {
      name: '祥瑞凤凰摆件',
      category: 'traditional-carving',
      subCategory: 'mythical-creatures',
      thirdCategory: 'phoenix-series',
      sizeInches: '10×6×8',
      weightGrams: 1800,
      isNew: false,
      isBestseller: true
    },
    {
      name: '福禄寿三星',
      category: 'traditional-carving',
      subCategory: 'religious-figures',
      thirdCategory: 'blessing-series',
      sizeInches: '8×4×10',
      weightGrams: 2200,
      isNew: false,
      isBestseller: true
    },
    {
      name: '观音菩萨像',
      category: 'traditional-carving',
      subCategory: 'religious-figures',
      thirdCategory: 'buddhist-series',
      sizeInches: '6×4×12',
      weightGrams: 1500,
      isNew: false,
      isBestseller: false
    },
    {
      name: '梅兰竹菊四君子',
      category: 'traditional-carving',
      subCategory: 'floral-patterns',
      thirdCategory: 'four-gentlemen',
      sizeInches: '16×2×8',
      weightGrams: 1200,
      isNew: true,
      isBestseller: false
    },
    {
      name: '荷花莲蓬雅韵',
      category: 'traditional-carving',
      subCategory: 'floral-patterns',
      thirdCategory: 'lotus-series',
      sizeInches: '14×10×6',
      weightGrams: 2000,
      isNew: false,
      isBestseller: true
    },
    {
      name: '现代抽象艺术品',
      category: 'modern-woodcraft',
      subCategory: 'abstract-art',
      thirdCategory: 'geometric-forms',
      sizeInches: '8×8×12',
      weightGrams: 1600,
      isNew: true,
      isBestseller: false
    },
    {
      name: '简约线条雕塑',
      category: 'modern-woodcraft',
      subCategory: 'abstract-art',
      thirdCategory: 'minimalist-design',
      sizeInches: '6×6×15',
      weightGrams: 1400,
      isNew: true,
      isBestseller: false
    },
    {
      name: '创意动物造型',
      category: 'modern-woodcraft',
      subCategory: 'animal-forms',
      thirdCategory: 'stylized-animals',
      sizeInches: '10×8×8',
      weightGrams: 1800,
      isNew: false,
      isBestseller: true
    },
    {
      name: '几何拼接艺术',
      category: 'modern-woodcraft',
      subCategory: 'geometric-art',
      thirdCategory: 'modular-design',
      sizeInches: '12×12×4',
      weightGrams: 2200,
      isNew: true,
      isBestseller: false
    },
    {
      name: '精美茶具套装',
      category: 'functional-wood',
      subCategory: 'tea-accessories',
      thirdCategory: 'tea-sets',
      sizeInches: '16×12×8',
      weightGrams: 3000,
      isNew: false,
      isBestseller: true
    },
    {
      name: '文房四宝盒',
      category: 'functional-wood',
      subCategory: 'study-accessories',
      thirdCategory: 'scholar-items',
      sizeInches: '14×10×6',
      weightGrams: 2500,
      isNew: false,
      isBestseller: false
    },
    {
      name: '香薰炉座',
      category: 'functional-wood',
      subCategory: 'incense-accessories',
      thirdCategory: 'burner-stands',
      sizeInches: '8×8×10',
      weightGrams: 1200,
      isNew: true,
      isBestseller: false
    },
    {
      name: '珠宝首饰盒',
      category: 'functional-wood',
      subCategory: 'storage-boxes',
      thirdCategory: 'jewelry-boxes',
      sizeInches: '12×8×6',
      weightGrams: 1800,
      isNew: false,
      isBestseller: true
    },
    {
      name: '古典花瓶架',
      category: 'functional-wood',
      subCategory: 'display-stands',
      thirdCategory: 'vase-stands',
      sizeInches: '10×10×20',
      weightGrams: 2800,
      isNew: false,
      isBestseller: false
    },
    {
      name: '禅意水景摆件',
      category: 'modern-woodcraft',
      subCategory: 'zen-art',
      thirdCategory: 'water-features',
      sizeInches: '18×12×8',
      weightGrams: 3500,
      isNew: true,
      isBestseller: true
    },
    {
      name: '山水意境雕刻',
      category: 'traditional-carving',
      subCategory: 'landscape-art',
      thirdCategory: 'mountain-water',
      sizeInches: '20×6×12',
      weightGrams: 2800,
      isNew: false,
      isBestseller: true
    },
    {
      name: '书法艺术屏风',
      category: 'traditional-carving',
      subCategory: 'calligraphy-art',
      thirdCategory: 'screen-panels',
      sizeInches: '24×2×18',
      weightGrams: 4000,
      isNew: false,
      isBestseller: false
    },
    {
      name: '现代灯具底座',
      category: 'functional-wood',
      subCategory: 'lighting-accessories',
      thirdCategory: 'lamp-bases',
      sizeInches: '8×8×12',
      weightGrams: 1600,
      isNew: true,
      isBestseller: false
    },
    {
      name: '艺术收纳盒',
      category: 'functional-wood',
      subCategory: 'storage-boxes',
      thirdCategory: 'decorative-boxes',
      sizeInches: '10×8×4',
      weightGrams: 1200,
      isNew: true,
      isBestseller: false
    }
  ]
};

async function createEnhancedProducts() {
  try {
    await client.connect();
    console.log('🔌 已连接到数据库');

    // 1. 创建三级分类结构
    console.log('📁 创建三级分类结构...');
    
    const categoryMap = {};
    
    // 创建一级分类
    for (const category of enhancedWoodCarvingData.categories) {
      await client.query(`
        INSERT INTO product_category (id, name, handle, mpath, category_level, category_path, is_active, is_internal, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [category.id, category.name, category.handle, category.id, category.level, category.name, true, false]);
      
      categoryMap[category.handle] = category.id;
      console.log(`  ✅ 创建一级分类: ${category.name}`);
    }

    // 创建二级和三级分类
    const subCategories = [
      // 传统木雕的二级分类
      { name: '神话生物', handle: 'mythical-creatures', parent: 'traditional-carving', level: 2 },
      { name: '宗教人物', handle: 'religious-figures', parent: 'traditional-carving', level: 2 },
      { name: '花鸟图案', handle: 'floral-patterns', parent: 'traditional-carving', level: 2 },
      { name: '山水风景', handle: 'landscape-art', parent: 'traditional-carving', level: 2 },
      { name: '书法艺术', handle: 'calligraphy-art', parent: 'traditional-carving', level: 2 },
      
      // 现代木艺的二级分类
      { name: '抽象艺术', handle: 'abstract-art', parent: 'modern-woodcraft', level: 2 },
      { name: '动物造型', handle: 'animal-forms', parent: 'modern-woodcraft', level: 2 },
      { name: '几何艺术', handle: 'geometric-art', parent: 'modern-woodcraft', level: 2 },
      { name: '禅意艺术', handle: 'zen-art', parent: 'modern-woodcraft', level: 2 },
      
      // 实用木器的二级分类
      { name: '茶具配件', handle: 'tea-accessories', parent: 'functional-wood', level: 2 },
      { name: '文房用品', handle: 'study-accessories', parent: 'functional-wood', level: 2 },
      { name: '香薰用品', handle: 'incense-accessories', parent: 'functional-wood', level: 2 },
      { name: '收纳盒类', handle: 'storage-boxes', parent: 'functional-wood', level: 2 },
      { name: '展示架类', handle: 'display-stands', parent: 'functional-wood', level: 2 },
      { name: '灯具配件', handle: 'lighting-accessories', parent: 'functional-wood', level: 2 }
    ];

    for (const subCat of subCategories) {
      const subCatId = generateId('pcat');
      const parentId = categoryMap[subCat.parent];
      const parentName = enhancedWoodCarvingData.categories.find(c => c.handle === subCat.parent).name;
      const categoryPath = `${parentName} / ${subCat.name}`;
      const mpath = `${parentId}.${subCatId}`;

      await client.query(`
        INSERT INTO product_category (id, name, handle, parent_category_id, mpath, category_level, category_path, is_active, is_internal, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [subCatId, subCat.name, subCat.handle, parentId, mpath, subCat.level, categoryPath, true, false]);
      
      categoryMap[subCat.handle] = subCatId;
      console.log(`  ✅ 创建二级分类: ${subCat.name}`);
    }

    // 创建三级分类（示例）
    const thirdCategories = [
      { name: '龙系列', handle: 'dragon-series', parent: 'mythical-creatures' },
      { name: '凤凰系列', handle: 'phoenix-series', parent: 'mythical-creatures' },
      { name: '祈福系列', handle: 'blessing-series', parent: 'religious-figures' },
      { name: '佛教系列', handle: 'buddhist-series', parent: 'religious-figures' },
      { name: '四君子', handle: 'four-gentlemen', parent: 'floral-patterns' },
      { name: '荷花系列', handle: 'lotus-series', parent: 'floral-patterns' },
      { name: '几何造型', handle: 'geometric-forms', parent: 'abstract-art' },
      { name: '极简设计', handle: 'minimalist-design', parent: 'abstract-art' },
      { name: '风格化动物', handle: 'stylized-animals', parent: 'animal-forms' },
      { name: '模块化设计', handle: 'modular-design', parent: 'geometric-art' },
      { name: '茶具套装', handle: 'tea-sets', parent: 'tea-accessories' },
      { name: '文人用品', handle: 'scholar-items', parent: 'study-accessories' },
      { name: '香炉架', handle: 'burner-stands', parent: 'incense-accessories' },
      { name: '首饰盒', handle: 'jewelry-boxes', parent: 'storage-boxes' },
      { name: '花瓶架', handle: 'vase-stands', parent: 'display-stands' },
      { name: '水景摆件', handle: 'water-features', parent: 'zen-art' },
      { name: '山水画', handle: 'mountain-water', parent: 'landscape-art' },
      { name: '屏风面板', handle: 'screen-panels', parent: 'calligraphy-art' },
      { name: '灯座', handle: 'lamp-bases', parent: 'lighting-accessories' },
      { name: '装饰盒', handle: 'decorative-boxes', parent: 'storage-boxes' }
    ];

    for (const thirdCat of thirdCategories) {
      const thirdCatId = generateId('pcat');
      const parentId = categoryMap[thirdCat.parent];
      
      // 获取父分类信息来构建路径
      const parentResult = await client.query('SELECT category_path FROM product_category WHERE id = $1', [parentId]);
      const parentPath = parentResult.rows[0]?.category_path || '';
      const categoryPath = `${parentPath} / ${thirdCat.name}`;
      const mpath = `${parentId}.${thirdCatId}`;

      await client.query(`
        INSERT INTO product_category (id, name, handle, parent_category_id, mpath, category_level, category_path, is_active, is_internal, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [thirdCatId, thirdCat.name, thirdCat.handle, parentId, mpath, 3, categoryPath, true, false]);
      
      categoryMap[thirdCat.handle] = thirdCatId;
      console.log(`  ✅ 创建三级分类: ${thirdCat.name}`);
    }

    console.log(`📊 分类创建完成，共创建 ${Object.keys(categoryMap).length} 个分类`);

    // 2. 创建产品集合
    console.log('📦 创建产品集合...');
    const collections = [
      { title: '新品上市', handle: 'new-arrivals', featured: true },
      { title: '热销爆款', handle: 'bestsellers', featured: true },
      { title: '传统经典', handle: 'traditional-classics', featured: false },
      { title: '现代创意', handle: 'modern-creative', featured: false },
      { title: '实用精品', handle: 'functional-premium', featured: false },
      { title: '大师作品', handle: 'master-pieces', featured: true }
    ];

    const collectionMap = {};
    for (const collection of collections) {
      const collectionId = generateId('pcol');
      await client.query(`
        INSERT INTO product_collection (id, title, handle, metadata, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [collectionId, collection.title, collection.handle, JSON.stringify({ featured: collection.featured })]);

      collectionMap[collection.handle] = collectionId;
      console.log(`  ✅ 创建集合: ${collection.title}`);
    }

    // 3. 创建20个增强版产品
    console.log('🎨 创建20个增强版木雕产品...');

    for (let i = 0; i < enhancedWoodCarvingData.productTemplates.length; i++) {
      const template = enhancedWoodCarvingData.productTemplates[i];
      const productId = generateId('prod');

      // 随机选择属性
      const designer = randomChoice(enhancedWoodCarvingData.designers);
      const material = randomChoice(enhancedWoodCarvingData.materials);
      const designConcept = randomChoice(enhancedWoodCarvingData.designConcepts);
      const usageScenario = randomChoice(enhancedWoodCarvingData.usageScenarios);
      const craftsmanship = randomChoice(enhancedWoodCarvingData.craftsmanshipSkills);
      const deliveryScope = randomChoice(enhancedWoodCarvingData.deliveryScopes);

      // 价格计算
      const purchasePriceCny = randomPrice(200, 2000);
      const salePriceUsd = (purchasePriceCny * 0.2).toFixed(2); // 约1:7的汇率加上利润

      // 选择集合
      let collectionId;
      if (template.isNew) {
        collectionId = collectionMap['new-arrivals'];
      } else if (template.isBestseller) {
        collectionId = collectionMap['bestsellers'];
      } else if (template.category === 'traditional-carving') {
        collectionId = collectionMap['traditional-classics'];
      } else if (template.category === 'modern-woodcraft') {
        collectionId = collectionMap['modern-creative'];
      } else {
        collectionId = collectionMap['functional-premium'];
      }

      // 创建产品
      await client.query(`
        INSERT INTO product (
          id, title, handle, description, material, design_concept, usage_scenario,
          craftsmanship, delivery_scope, designer_name, is_new_product, is_bestseller,
          purchase_price_cny, sale_price_usd, size_inches, weight_grams,
          status, collection_id, thumbnail, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [
        productId, template.name, template.name.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, ''),
        `精美的${template.name}，采用${material}精心雕刻而成，展现传统工艺与现代美学的完美结合。`,
        material, designConcept, usageScenario, craftsmanship, deliveryScope, designer,
        template.isNew, template.isBestseller, purchasePriceCny, salePriceUsd,
        template.sizeInches, template.weightGrams, 'published', collectionId,
        `https://images.unsplash.com/photo-${1500000000000 + i}?w=400`, // 占位图片
      ]);

      // 创建产品变体
      const variantId = generateId('variant');
      const sku = `WC${String(i + 1).padStart(3, '0')}-${material.substring(0, 2).toUpperCase()}`;

      await client.query(`
        INSERT INTO product_variant (
          id, title, product_id, sku, manage_inventory, allow_backorder,
          weight, length, width, height, material, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [
        variantId, 'Default', productId, sku, false, false,
        template.weightGrams / 1000, // 转换为公斤
        parseFloat(template.sizeInches.split('×')[0]),
        parseFloat(template.sizeInches.split('×')[1]),
        parseFloat(template.sizeInches.split('×')[2]),
        material
      ]);

      // 关联产品与分类
      const categoryId = categoryMap[template.thirdCategory];
      if (categoryId) {
        await client.query(`
          INSERT INTO product_category_product (product_id, product_category_id)
          VALUES ($1, $2)
          ON CONFLICT (product_id, product_category_id) DO NOTHING
        `, [productId, categoryId]);
      }

      console.log(`  ✅ 创建产品 ${i + 1}/20: ${template.name} (${designer}作品)`);
    }

    console.log('\n🎉 增强版木雕产品数据创建完成！');
    console.log('\n📊 数据统计:');
    console.log(`  📁 分类: ${Object.keys(categoryMap).length} 个（包含三级分类）`);
    console.log(`  📦 集合: ${collections.length} 个`);
    console.log(`  🎨 产品: ${enhancedWoodCarvingData.productTemplates.length} 个`);
    console.log(`  👨‍🎨 设计师: ${enhancedWoodCarvingData.designers.length} 位`);

    console.log('\n🔍 可以通过以下方式查看数据:');
    console.log('  管理后台: http://localhost:9000/app/login');
    console.log('  完整产品信息视图: SELECT * FROM product_full_info;');
    console.log('  分类层级视图: SELECT * FROM product_category_hierarchy;');

  } catch (error) {
    console.error('❌ 创建数据时出错:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行脚本
createEnhancedProducts();
