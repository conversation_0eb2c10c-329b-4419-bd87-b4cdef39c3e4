const { createClient } = require('@sanity/client');

// Sanity 客户端配置
const sanityClient = createClient({
  projectId: process.env.SANITY_PROJECT_ID || 'qqvccmla',
  dataset: process.env.SANITY_DATASET || 'production',
  apiVersion: process.env.SANITY_API_VERSION || '2025-08-15',
  token: process.env.SANITY_API_TOKEN || 'sk7e8ed8JSEHUu9T7w4t5iZiwwBztstYhqEiSb1d5hprDOy5W2ngaMukPrAhIdcRTaV2b9MXwZOZ6iZqPV7yJ5EJj1jTB5DwNHZoTjtmuxOIQDsatycDeJZPTH8iTdvs5dcPUdSm1r91Q23zmFv7dAe3IjG89X2x8LiNAmWeqxPa32ThoHrQ',
  useCdn: false
});

// Medusa API 配置
const MEDUSA_API_URL = process.env.MEDUSA_BACKEND_URL || 'http://localhost:9000/store';
const PUBLISHABLE_KEY = process.env.MEDUSA_PUBLISHABLE_KEY || 'pk_f3bf2b6ad7a6898050f237e15b93dea6dac071ae49c29bfbe5e9b1dad90ee2b1';

async function fetchMedusaProducts() {
  try {
    // 直接从数据库获取所有产品，包括新创建的
    const { Client } = require('pg');
    const client = new Client({
      connectionString: 'postgresql://localhost:5432/medusa_dev'
    });

    await client.connect();

    const result = await client.query(`
      SELECT id, title, handle, description, material, weight, length, height, width,
             origin_country, created_at, updated_at
      FROM product
      WHERE status = 'published'
      AND deleted_at IS NULL
      ORDER BY created_at DESC
    `);

    await client.end();

    console.log(`📦 从数据库获取到 ${result.rows.length} 个产品`);
    return result.rows;
  } catch (error) {
    console.error('Error fetching Medusa products from database:', error);
    return [];
  }
}

async function syncProductToSanity(medusaProduct) {
  try {
    const sanityProduct = {
      _id: medusaProduct.id,
      _type: 'product',
      title: medusaProduct.title,
      handle: medusaProduct.handle,
      description: medusaProduct.description,
      thumbnail: medusaProduct.thumbnail,
      internalTitle: medusaProduct.title,
      pathname: {
        _type: 'slug',
        current: `/products/${medusaProduct.handle}`
      },
      indexable: true,
      // 添加其他需要的字段
      medusaData: {
        id: medusaProduct.id,
        handle: medusaProduct.handle,
        created_at: medusaProduct.created_at,
        updated_at: medusaProduct.updated_at,
        thumbnail: medusaProduct.thumbnail,
        images: medusaProduct.images || [],
        material: medusaProduct.material,
        weight: medusaProduct.weight,
        length: medusaProduct.length,
        height: medusaProduct.height,
        width: medusaProduct.width,
      }
    };

    // 使用 createOrReplace 来确保文档存在
    const result = await sanityClient.createOrReplace(sanityProduct);
    console.log(`✅ 同步产品: ${medusaProduct.title} (${medusaProduct.id})`);
    return result;
  } catch (error) {
    console.error(`❌ 同步产品失败 ${medusaProduct.id}:`, error);
    return null;
  }
}

async function syncAllProducts() {
  console.log('🚀 开始同步 Medusa 产品到 Sanity...');
  
  const medusaProducts = await fetchMedusaProducts();
  console.log(`📦 找到 ${medusaProducts.length} 个 Medusa 产品`);

  if (medusaProducts.length === 0) {
    console.log('❌ 没有找到产品，请检查 Medusa API 连接');
    return;
  }

  let successCount = 0;
  let failCount = 0;

  for (const product of medusaProducts) {
    const result = await syncProductToSanity(product);
    if (result) {
      successCount++;
    } else {
      failCount++;
    }
    
    // 添加小延迟避免 API 限制
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('\n📊 同步完成统计:');
  console.log(`✅ 成功: ${successCount} 个产品`);
  console.log(`❌ 失败: ${failCount} 个产品`);
  console.log(`📦 总计: ${medusaProducts.length} 个产品`);
}

// 如果直接运行此脚本
if (require.main === module) {
  syncAllProducts().catch(console.error);
}

module.exports = {
  syncAllProducts,
  syncProductToSanity,
  fetchMedusaProducts
};
