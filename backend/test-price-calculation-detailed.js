async function testPriceCalculationDetailed() {
  const fetch = (await import('node-fetch')).default;
  console.log('🧪 详细测试价格计算...\n');

  const baseUrl = 'http://localhost:9000';
  const publishableKey = 'pk_f3bf2b6ad7a6898050f237e15b93dea6dac071ae49c29bfbe5e9b1dad90ee2b1';
  const regionId = 'reg_mecot5lgy79vtq0q8'; // North America region
  const productHandle = 'tomo-carved-wooden-brown-bear';

  try {
    // 1. 测试基本产品获取
    console.log('1️⃣ 测试基本产品获取...');
    const productResponse = await fetch(
      `${baseUrl}/store/products?handle=${productHandle}&region_id=${regionId}&fields=*variants.calculated_price,+variants.inventory_quantity,+variants.prices,+variants.price_set`,
      {
        headers: {
          'x-publishable-api-key': publishableKey,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!productResponse.ok) {
      throw new Error(`产品请求失败: ${productResponse.status}`);
    }

    const productData = await productResponse.json();
    const product = productData.products[0];

    if (!product) {
      throw new Error('未找到产品');
    }

    console.log(`✅ 产品: ${product.title}`);
    console.log(`   ID: ${product.id}`);
    console.log(`   变体数量: ${product.variants?.length || 0}`);

    // 2. 检查变体详情
    console.log('\n2️⃣ 检查变体详情...');
    const variant = product.variants[0];
    if (variant) {
      console.log(`   变体 ID: ${variant.id}`);
      console.log(`   变体标题: ${variant.title}`);
      console.log(`   calculated_price:`, variant.calculated_price);
      console.log(`   prices:`, variant.prices);
      console.log(`   price_set:`, variant.price_set);
    }

    // 3. 测试直接价格计算 API
    console.log('\n3️⃣ 测试直接价格计算 API...');
    const priceCalculationResponse = await fetch(
      `${baseUrl}/store/products/${product.id}/variants/${variant.id}/prices?region_id=${regionId}`,
      {
        headers: {
          'x-publishable-api-key': publishableKey,
          'Content-Type': 'application/json',
        },
      }
    );

    if (priceCalculationResponse.ok) {
      const priceData = await priceCalculationResponse.json();
      console.log('✅ 价格计算 API 响应:', priceData);
    } else {
      console.log(`❌ 价格计算 API 失败: ${priceCalculationResponse.status}`);
    }

    // 4. 测试购物车添加（这会触发价格计算）
    console.log('\n4️⃣ 测试购物车添加...');
    
    // 首先创建购物车
    const cartResponse = await fetch(`${baseUrl}/store/carts`, {
      method: 'POST',
      headers: {
        'x-publishable-api-key': publishableKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        region_id: regionId,
      }),
    });

    if (!cartResponse.ok) {
      throw new Error(`创建购物车失败: ${cartResponse.status}`);
    }

    const cartData = await cartResponse.json();
    const cartId = cartData.cart.id;
    console.log(`✅ 购物车创建成功: ${cartId}`);

    // 尝试添加商品到购物车
    const addToCartResponse = await fetch(`${baseUrl}/store/carts/${cartId}/line-items`, {
      method: 'POST',
      headers: {
        'x-publishable-api-key': publishableKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        variant_id: variant.id,
        quantity: 1,
      }),
    });

    if (addToCartResponse.ok) {
      const updatedCart = await addToCartResponse.json();
      console.log('✅ 添加到购物车成功');
      console.log('   购物车商品:', updatedCart.cart.items?.length || 0);
      if (updatedCart.cart.items?.length > 0) {
        const item = updatedCart.cart.items[0];
        console.log(`   商品价格: ${item.unit_price || 'null'}`);
        console.log(`   总价: ${item.total || 'null'}`);
      }
    } else {
      const errorText = await addToCartResponse.text();
      console.log(`❌ 添加到购物车失败: ${addToCartResponse.status}`);
      console.log(`   错误信息: ${errorText}`);
    }

    // 5. 测试 Admin API 获取价格
    console.log('\n5️⃣ 测试 Admin API 获取价格...');
    const adminProductResponse = await fetch(
      `${baseUrl}/admin/products/${product.id}?fields=*variants.prices`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (adminProductResponse.ok) {
      const adminProductData = await adminProductResponse.json();
      const adminVariant = adminProductData.product.variants[0];
      console.log('✅ Admin API 变体价格:', adminVariant.prices);
    } else {
      console.log(`❌ Admin API 失败: ${adminProductResponse.status}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

if (require.main === module) {
  testPriceCalculationDetailed().catch(console.error);
}

module.exports = { testPriceCalculationDetailed };
