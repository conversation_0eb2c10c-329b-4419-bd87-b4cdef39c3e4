const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

// Mokuomo 产品分类
const mokuomoCategories = [
  { 
    name: 'Combos', 
    handle: 'combos', 
    description: 'Wooden sculpture combo sets and collections',
    mpath: 'combos.'
  },
  { 
    name: 'Best Sellers', 
    handle: 'best-sellers', 
    description: 'Most popular wooden sculptures',
    mpath: 'best-sellers.'
  },
  { 
    name: 'Wildlife', 
    handle: 'wildlife', 
    description: 'Wild animal wooden sculptures',
    mpath: 'wildlife.'
  },
  { 
    name: 'Bears', 
    handle: 'bears', 
    description: 'Carved wooden bear sculptures',
    mpath: 'bears.'
  },
  { 
    name: 'Cats', 
    handle: 'cats', 
    description: 'Carved wooden cat sculptures',
    mpath: 'cats.'
  },
  { 
    name: 'Dogs', 
    handle: 'dogs', 
    description: 'Carved wooden dog sculptures',
    mpath: 'dogs.'
  },
  { 
    name: 'Fun Finds', 
    handle: 'fun-finds', 
    description: 'Unique and playful wooden sculptures',
    mpath: 'fun-finds.'
  },
  { 
    name: 'Birds', 
    handle: 'birds', 
    description: 'Carved wooden bird sculptures',
    mpath: 'birds.'
  }
];

// 产品分类映射（基于产品标题关键词）
const productCategoryMapping = {
  'bear': 'bears',
  'cat': 'cats', 
  'dog': 'dogs',
  'bird': 'birds',
  'fox': 'wildlife',
  'whale': 'wildlife',
  'panda': 'wildlife',
  'giraffe': 'wildlife',
  'squirrel': 'wildlife',
  'penguin': 'wildlife',
  'capybara': 'wildlife',
  'combo': 'combos',
  'series': 'combos',
  'family': 'combos',
  'set': 'combos',
  'crew': 'fun-finds',
  'friends': 'fun-finds',
  'yoga': 'fun-finds'
};

function categorizeProduct(title) {
  const titleLower = title.toLowerCase();
  
  for (const [keyword, category] of Object.entries(productCategoryMapping)) {
    if (titleLower.includes(keyword)) {
      return category;
    }
  }
  
  // 默认分类
  return 'fun-finds';
}

async function createProductCategories(client) {
  console.log('📁 创建产品分类...');
  const createdCategories = {};
  
  for (const category of mokuomoCategories) {
    try {
      // 先检查是否已存在
      const existingResult = await client.query(
        'SELECT id FROM product_category WHERE handle = $1 AND deleted_at IS NULL', 
        [category.handle]
      );
      
      if (existingResult.rows.length > 0) {
        createdCategories[category.handle] = existingResult.rows[0].id;
        console.log(`✅ 分类已存在: ${category.name} (${category.handle})`);
        continue;
      }
      
      const categoryId = `pcat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const insertQuery = `
        INSERT INTO product_category (
          id, name, description, handle, mpath, is_active, is_internal, rank,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
        RETURNING id
      `;
      
      const result = await client.query(insertQuery, [
        categoryId, 
        category.name, 
        category.description,
        category.handle, 
        category.mpath,
        true, // is_active
        false, // is_internal
        0 // rank
      ]);
      
      createdCategories[category.handle] = result.rows[0].id;
      console.log(`✅ 创建分类: ${category.name} (${category.handle})`);
      
    } catch (error) {
      console.error(`❌ 创建分类失败 ${category.name}:`, error.message);
    }
  }
  
  return createdCategories;
}

async function linkProductToCategory(client, productId, categoryId) {
  try {
    // 检查是否已经关联
    const existingResult = await client.query(`
      SELECT 1 FROM product_category_product 
      WHERE product_id = $1 AND product_category_id = $2
    `, [productId, categoryId]);
    
    if (existingResult.rows.length > 0) {
      return false; // 已存在
    }

    // 创建关联
    await client.query(`
      INSERT INTO product_category_product (product_id, product_category_id)
      VALUES ($1, $2)
    `, [productId, categoryId]);
    
    return true; // 新创建
  } catch (error) {
    console.error(`关联产品到分类失败:`, error.message);
    return false;
  }
}

async function createCategoriesAndLinkProducts() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 开始创建产品分类并关联产品...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 创建产品分类
    const categories = await createProductCategories(client);
    console.log(`📁 处理了 ${Object.keys(categories).length} 个分类`);

    // 2. 获取所有新导入的产品（Carved 或 Wooden）
    const productsResult = await client.query(`
      SELECT id, title, handle 
      FROM product 
      WHERE (title LIKE '%Carved%' OR title LIKE '%Wooden%')
      AND status = 'published'
      AND deleted_at IS NULL
      ORDER BY created_at DESC
    `);
    
    const products = productsResult.rows;
    console.log(`📦 找到 ${products.length} 个需要分类的产品`);

    let successCount = 0;
    let existingCount = 0;
    let failCount = 0;

    // 3. 为每个产品分配分类
    for (const product of products) {
      try {
        // 确定产品分类
        const categoryHandle = categorizeProduct(product.title);
        const categoryId = categories[categoryHandle];
        
        if (!categoryId) {
          console.log(`⚠️  未找到分类 ${categoryHandle}: ${product.title}`);
          failCount++;
          continue;
        }

        // 关联产品到分类
        const isNew = await linkProductToCategory(client, product.id, categoryId);
        
        if (isNew) {
          console.log(`✅ 新关联: ${product.title} -> ${categoryHandle}`);
          successCount++;
        } else {
          console.log(`✅ 已关联: ${product.title} -> ${categoryHandle}`);
          existingCount++;
        }
        
      } catch (error) {
        console.error(`❌ 处理产品失败 ${product.title}:`, error.message);
        failCount++;
      }
    }

    console.log('\n📊 分类关联完成统计:');
    console.log(`✅ 新关联: ${successCount} 个产品`);
    console.log(`✅ 已存在: ${existingCount} 个产品`);
    console.log(`❌ 失败: ${failCount} 个产品`);
    console.log(`📦 总计: ${products.length} 个产品`);
    
    // 4. 显示每个分类的产品数量
    console.log('\n📁 各分类产品数量:');
    for (const [handle, categoryId] of Object.entries(categories)) {
      const countResult = await client.query(`
        SELECT COUNT(*) as count 
        FROM product_category_product pcp
        INNER JOIN product p ON pcp.product_id = p.id
        WHERE pcp.product_category_id = $1 AND p.deleted_at IS NULL
      `, [categoryId]);
      
      const count = countResult.rows[0].count;
      console.log(`  ${handle}: ${count} 个产品`);
    }
    
    if (successCount > 0 || existingCount > 0) {
      console.log('\n🎉 产品分类创建和关联完成！');
      console.log('💡 建议重新同步产品到 Sanity: node sync-products-to-sanity.js');
    }

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createCategoriesAndLinkProducts().catch(console.error);
}

module.exports = { createCategoriesAndLinkProducts };
