import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";
import { Client } from "pg";

// 数据库连接配置
const dbConfig = {
  connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/medusa_dev'
};

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
): Promise<void> {
  const variantId = req.params.id;

  if (!variantId) {
    res.status(400).json({ error: "Variant ID is required" });
    return;
  }

  const client = new Client(dbConfig);

  try {
    await client.connect();

    const result = await client.query(`
      SELECT 
        pv.id as variant_id,
        pv.title as variant_title,
        p.amount,
        p.currency_code,
        prod.title as product_title
      FROM product_variant pv 
      INNER JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id 
      INNER JOIN price p ON pvps.price_set_id = p.price_set_id 
      INNER JOIN product prod ON pv.product_id = prod.id
      WHERE pv.id = $1 
      AND pv.deleted_at IS NULL 
      AND pvps.deleted_at IS NULL 
      AND p.deleted_at IS NULL
    `, [variantId]);

    if (result.rows.length > 0) {
      const row = result.rows[0];
      res.json({
        variant_id: row.variant_id,
        variant_title: row.variant_title,
        product_title: row.product_title,
        amount: row.amount,
        currency_code: row.currency_code,
        formatted_price: `$${(row.amount / 100).toFixed(2)}`
      });
    } else {
      res.status(404).json({ error: "Price not found for variant" });
    }
  } catch (error) {
    console.error('Error fetching variant price:', error);
    res.status(500).json({ error: "Internal server error" });
  } finally {
    await client.end();
  }
}
