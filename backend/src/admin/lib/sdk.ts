import Medusa from "@medusajs/js-sdk";

// 在浏览器环境中安全地获取环境变量
export const backendUrl = (typeof process !== "undefined" && process.env?.MEDUSA_ADMIN_BACKEND_URL) || "http://localhost:9000";

export const sdk = new Medusa({
  baseUrl: backendUrl,
  auth: {
    type: "session",
  },
});

// useful when you want to call the BE from the console and try things out quickly
if (typeof window !== "undefined") {
  (window as any).__sdk = sdk;
}
