import { SubscriberConfig } from "@medusajs/medusa";
import { createClient } from '@sanity/client';

// Sanity 客户端配置
const sanityClient = createClient({
  projectId: process.env.SANITY_PROJECT_ID || 'qqvccmla',
  dataset: process.env.SANITY_DATASET || 'production',
  apiVersion: process.env.SANITY_API_VERSION || '2025-08-15',
  token: process.env.SANITY_API_TOKEN || 'sk7e8ed8JSEHUu9T7w4t5iZiwwBztstYhqEiSb1d5hprDOy5W2ngaMukPrAhIdcRTaV2b9MXwZOZ6iZqPV7yJ5EJj1jTB5DwNHZoTjtmuxOIQDsatycDeJZPTH8iTdvs5dcPUdSm1r91Q23zmFv7dAe3IjG89X2x8LiNAmWeqxPa32ThoHrQ',
  useCdn: false
});

async function syncProductToSanity(productId: string, productService: any) {
  try {
    console.log(`🔄 同步产品到 Sanity: ${productId}`);
    
    // 获取完整的产品数据
    const product = await productService.retrieve(productId, {
      relations: ["images", "variants", "collection", "tags"]
    });

    const sanityProduct = {
      _id: product.id,
      _type: 'product',
      title: product.title,
      handle: product.handle,
      description: product.description,
      thumbnail: product.thumbnail,
      internalTitle: product.title,
      pathname: {
        _type: 'slug',
        current: `/products/${product.handle}`
      },
      indexable: true,
      medusaData: {
        id: product.id,
        handle: product.handle,
        created_at: product.created_at,
        updated_at: product.updated_at,
        collection_id: product.collection_id,
        thumbnail: product.thumbnail,
        images: product.images || [],
        material: product.material,
        weight: product.weight,
        length: product.length,
        height: product.height,
        width: product.width,
      }
    };

    // 使用 createOrReplace 来确保文档存在
    const result = await sanityClient.createOrReplace(sanityProduct);
    console.log(`✅ 产品同步成功: ${product.title} (${product.id})`);
    return result;
  } catch (error) {
    console.error(`❌ 产品同步失败 ${productId}:`, error);
    return null;
  }
}

async function deleteProductFromSanity(productId: string) {
  try {
    console.log(`🗑️ 从 Sanity 删除产品: ${productId}`);
    await sanityClient.delete(productId);
    console.log(`✅ 产品删除成功: ${productId}`);
  } catch (error) {
    console.error(`❌ 产品删除失败 ${productId}:`, error);
  }
}

export default async function productSyncHandler({
  data,
  eventName,
  container,
}: any) {
  const productService = container.resolve("productService");

  switch (eventName) {
    case "product.created":
      console.log(`📦 产品创建事件: ${data.id}`);
      await syncProductToSanity(data.id, productService);
      break;

    case "product.updated":
      console.log(`📝 产品更新事件: ${data.id}`);
      await syncProductToSanity(data.id, productService);
      break;

    case "product.deleted":
      console.log(`🗑️ 产品删除事件: ${data.id}`);
      await deleteProductFromSanity(data.id);
      break;

    default:
      console.log(`🔔 未处理的产品事件: ${eventName}`);
  }
}

export const config: SubscriberConfig = {
  event: [
    "product.created",
    "product.updated", 
    "product.deleted"
  ],
  context: {
    subscriberId: "product-sanity-sync",
  },
};
