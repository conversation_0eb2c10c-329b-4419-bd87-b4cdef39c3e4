const fetch = require('node-fetch').default || require('node-fetch');

// Medusa Admin API 配置
const MEDUSA_ADMIN_URL = 'http://localhost:9000/admin';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'supersecret';

// 中文 handle 到英文 handle 的映射
const handleMappings = {
  '威武神龙雕刻-1': 'mighty-dragon-carving',
  '祥瑞凤凰摆件-2': 'auspicious-phoenix-ornament',
  '福禄寿三星-3': 'three-stars-of-fortune',
  '观音菩萨像-4': 'guanyin-buddha-statue',
  '梅兰竹菊四君子-5': 'four-gentlemen-carving',
  '荷花莲蓬雅韵-6': 'lotus-pond-elegance',
  '现代抽象艺术品-7': 'modern-abstract-art',
  '简约线条雕塑-8': 'minimalist-line-sculpture',
  '创意动物造型-9': 'creative-animal-sculpture',
  '几何拼接艺术-10': 'geometric-mosaic-art',
  '精美茶具套装-11': 'exquisite-tea-set',
  '文房四宝盒-12': 'four-treasures-study-box',
  '香薰炉座-13': 'incense-burner-stand',
  '珠宝首饰盒-14': 'jewelry-box',
  '古典花瓶架-15': 'classical-vase-stand',
  '禅意水景摆件-16': 'zen-water-landscape',
  '山水意境雕刻-17': 'landscape-mood-carving',
  '书法艺术屏风-18': 'calligraphy-art-screen',
  '现代灯具底座-19': 'modern-lamp-base',
  '艺术收纳盒-20': 'artistic-storage-box'
};

async function loginToAdmin() {
  try {
    const response = await fetch(`${MEDUSA_ADMIN_URL}/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
      }),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.access_token;
  } catch (error) {
    console.error('❌ Admin login failed:', error);
    return null;
  }
}

async function updateProductHandle(productId, newHandle, accessToken) {
  try {
    console.log(`🔄 Updating product ${productId} handle to: ${newHandle}`);
    
    const response = await fetch(`${MEDUSA_ADMIN_URL}/products/${productId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        handle: newHandle,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Update failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log(`✅ Successfully updated: ${data.product.title} -> ${newHandle}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to update product ${productId}:`, error.message);
    return false;
  }
}

async function getAllProducts() {
  try {
    const response = await fetch('http://localhost:9000/store/products', {
      headers: {
        'x-publishable-api-key': 'pk_f3bf2b6ad7a6898050f237e15b93dea6dac071ae49c29bfbe5e9b1dad90ee2b1',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch products: ${response.status}`);
    }

    const data = await response.json();
    return data.products || [];
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
}

async function fixProductHandles() {
  console.log('🚀 开始修复产品 handle...');
  
  // 1. 登录管理员
  const accessToken = await loginToAdmin();
  if (!accessToken) {
    console.log('❌ 无法登录管理员账户，请检查凭据');
    return;
  }
  console.log('✅ 管理员登录成功');

  // 2. 获取所有产品
  const products = await getAllProducts();
  console.log(`📦 找到 ${products.length} 个产品`);

  // 3. 找到需要更新的产品
  const productsToUpdate = products.filter(product => 
    handleMappings.hasOwnProperty(product.handle)
  );

  console.log(`🔍 找到 ${productsToUpdate.length} 个需要更新 handle 的产品:`);
  productsToUpdate.forEach(product => {
    console.log(`  - ${product.title} (${product.handle}) -> ${handleMappings[product.handle]}`);
  });

  if (productsToUpdate.length === 0) {
    console.log('✅ 所有产品的 handle 都已经是英文，无需更新');
    return;
  }

  // 4. 批量更新产品 handle
  let successCount = 0;
  let failCount = 0;

  for (const product of productsToUpdate) {
    const newHandle = handleMappings[product.handle];
    const success = await updateProductHandle(product.id, newHandle, accessToken);
    
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
    
    // 添加小延迟避免 API 限制
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\n📊 更新完成统计:');
  console.log(`✅ 成功: ${successCount} 个产品`);
  console.log(`❌ 失败: ${failCount} 个产品`);
  console.log(`📦 总计: ${productsToUpdate.length} 个产品`);
  
  if (successCount > 0) {
    console.log('\n🎉 产品 handle 修复完成！现在所有产品都有英文 handle，可以正常访问了。');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixProductHandles().catch(console.error);
}

module.exports = {
  fixProductHandles,
  handleMappings
};
