const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function fixSalesChannels() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 开始修复产品销售渠道关联...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 获取默认销售渠道 ID
    const channelResult = await client.query('SELECT id FROM sales_channel LIMIT 1');
    const defaultChannelId = channelResult.rows[0]?.id;
    
    if (!defaultChannelId) {
      console.log('❌ 没有找到销售渠道');
      return;
    }
    
    console.log(`📢 默认销售渠道: ${defaultChannelId}`);

    // 获取所有没有关联销售渠道的产品
    const productsResult = await client.query(`
      SELECT p.id, p.title 
      FROM product p 
      LEFT JOIN product_sales_channel psc ON p.id = psc.product_id 
      WHERE psc.product_id IS NULL 
      AND p.status = 'published'
    `);
    
    const unlinkedProducts = productsResult.rows;
    console.log(`📦 找到 ${unlinkedProducts.length} 个未关联销售渠道的产品`);

    if (unlinkedProducts.length === 0) {
      console.log('✅ 所有产品都已关联销售渠道');
      return;
    }

    // 批量关联产品到销售渠道
    let successCount = 0;
    let failCount = 0;

    for (const product of unlinkedProducts) {
      try {
        const relationId = `psc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await client.query(`
          INSERT INTO product_sales_channel (id, product_id, sales_channel_id)
          VALUES ($1, $2, $3)
          ON CONFLICT DO NOTHING
        `, [relationId, product.id, defaultChannelId]);
        
        console.log(`✅ 关联产品: ${product.title}`);
        successCount++;
      } catch (error) {
        console.error(`❌ 关联失败 ${product.title}:`, error.message);
        failCount++;
      }
    }

    console.log('\n📊 修复完成统计:');
    console.log(`✅ 成功: ${successCount} 个产品`);
    console.log(`❌ 失败: ${failCount} 个产品`);
    console.log(`📦 总计: ${unlinkedProducts.length} 个产品`);
    
    if (successCount > 0) {
      console.log('\n🎉 产品销售渠道关联修复完成！现在所有产品都应该在 Store API 中可见。');
    }

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixSalesChannels().catch(console.error);
}

module.exports = { fixSalesChannels };
