const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function testFinalFunctionality() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🧪 开始最终功能测试...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 测试产品数据完整性
    console.log('\n📋 测试产品数据完整性...');
    const productsResult = await client.query(`
      SELECT 
        p.id,
        p.title,
        p.handle,
        p.description,
        COUNT(pv.id) as variant_count
      FROM product p 
      LEFT JOIN product_variant pv ON p.id = pv.product_id 
      WHERE p.deleted_at IS NULL 
      AND pv.deleted_at IS NULL
      AND (p.title LIKE '%Carved%' OR p.title LIKE '%Wooden%')
      GROUP BY p.id, p.title, p.handle, p.description
      ORDER BY p.title
    `);
    
    console.log(`找到 ${productsResult.rows.length} 个 Mokuomo 产品`);
    
    // 2. 测试变体和价格数据
    console.log('\n💰 测试变体和价格数据...');
    const variantPricesResult = await client.query(`
      SELECT 
        pv.id as variant_id,
        pv.title as variant_title,
        p.title as product_title,
        pr.amount,
        pr.currency_code,
        CASE 
          WHEN pr.amount IS NOT NULL THEN '✅ 有价格'
          ELSE '❌ 无价格'
        END as price_status
      FROM product_variant pv 
      INNER JOIN product p ON pv.product_id = p.id 
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id AND pvps.deleted_at IS NULL
      LEFT JOIN price pr ON pvps.price_set_id = pr.price_set_id AND pr.deleted_at IS NULL
      WHERE p.deleted_at IS NULL 
      AND pv.deleted_at IS NULL
      AND (p.title LIKE '%Carved%' OR p.title LIKE '%Wooden%')
      ORDER BY p.title, pv.title
    `);
    
    const withPrice = variantPricesResult.rows.filter(row => row.amount !== null);
    const withoutPrice = variantPricesResult.rows.filter(row => row.amount === null);
    
    console.log(`总变体数: ${variantPricesResult.rows.length}`);
    console.log(`有价格的变体: ${withPrice.length}`);
    console.log(`无价格的变体: ${withoutPrice.length}`);
    
    if (withoutPrice.length > 0) {
      console.log('\n❌ 无价格的变体:');
      withoutPrice.forEach(row => {
        console.log(`  - ${row.variant_title} (${row.product_title})`);
      });
    }

    // 3. 测试产品选项
    console.log('\n🎛️ 测试产品选项...');
    const optionsResult = await client.query(`
      SELECT 
        p.title as product_title,
        po.title as option_title,
        COUNT(pov.id) as value_count
      FROM product p 
      INNER JOIN product_option po ON p.id = po.product_id 
      LEFT JOIN product_option_value pov ON po.id = pov.option_id 
      WHERE p.deleted_at IS NULL 
      AND po.deleted_at IS NULL
      AND pov.deleted_at IS NULL
      AND (p.title LIKE '%Carved%' OR p.title LIKE '%Wooden%')
      GROUP BY p.title, po.title
      ORDER BY p.title, po.title
    `);
    
    console.log(`产品选项总数: ${optionsResult.rows.length}`);
    
    // 按选项类型分组
    const optionsByType = optionsResult.rows.reduce((acc, row) => {
      if (!acc[row.option_title]) {
        acc[row.option_title] = 0;
      }
      acc[row.option_title]++;
      return acc;
    }, {});
    
    console.log('选项类型分布:');
    Object.entries(optionsByType).forEach(([type, count]) => {
      console.log(`  - ${type}: ${count} 个产品`);
    });

    // 4. 测试产品分类
    console.log('\n📂 测试产品分类...');
    const categoriesResult = await client.query(`
      SELECT 
        pc.name as category_name,
        COUNT(pcp.product_id) as product_count
      FROM product_category pc 
      LEFT JOIN product_category_product pcp ON pc.id = pcp.product_category_id 
      WHERE pc.deleted_at IS NULL
      GROUP BY pc.name
      ORDER BY product_count DESC
    `);
    
    console.log(`产品分类总数: ${categoriesResult.rows.length}`);
    categoriesResult.rows.forEach(row => {
      console.log(`  - ${row.category_name}: ${row.product_count} 个产品`);
    });

    // 5. 测试特定变体（错误中提到的）
    console.log('\n🔍 测试特定变体...');
    const specificVariantId = 'variant_1755366640268_ae3h859og';
    const specificVariantResult = await client.query(`
      SELECT 
        pv.id as variant_id,
        pv.title as variant_title,
        p.title as product_title,
        pr.amount,
        pr.currency_code
      FROM product_variant pv 
      INNER JOIN product p ON pv.product_id = p.id 
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id AND pvps.deleted_at IS NULL
      LEFT JOIN price pr ON pvps.price_set_id = pr.price_set_id AND pr.deleted_at IS NULL
      WHERE pv.id = $1
    `, [specificVariantId]);
    
    if (specificVariantResult.rows.length > 0) {
      const variant = specificVariantResult.rows[0];
      console.log(`✅ 找到变体: ${variant.variant_title}`);
      console.log(`  产品: ${variant.product_title}`);
      console.log(`  价格: ${variant.amount ? `$${(variant.amount / 100).toFixed(2)}` : '无价格'}`);
    } else {
      console.log(`❌ 未找到变体: ${specificVariantId}`);
    }

    // 6. 生成测试报告
    console.log('\n📊 测试报告总结:');
    console.log('='.repeat(50));
    console.log(`✅ Mokuomo 产品数量: ${productsResult.rows.length}`);
    console.log(`✅ 产品变体数量: ${variantPricesResult.rows.length}`);
    console.log(`✅ 有价格的变体: ${withPrice.length}`);
    console.log(`✅ 产品选项数量: ${optionsResult.rows.length}`);
    console.log(`✅ 产品分类数量: ${categoriesResult.rows.length}`);
    
    if (withoutPrice.length === 0) {
      console.log('🎉 所有变体都有价格数据！');
    } else {
      console.log(`⚠️  ${withoutPrice.length} 个变体缺少价格数据`);
    }
    
    console.log('\n🚀 建议测试步骤:');
    console.log('1. 访问产品列表页面: http://localhost:3000/us/products');
    console.log('2. 访问产品详情页面: http://localhost:3000/us/products/tomo-carved-wooden-brown-bear');
    console.log('3. 测试产品选项选择（Size 和 Color）');
    console.log('4. 测试添加购物车功能');
    console.log('5. 测试产品分类筛选');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testFinalFunctionality().catch(console.error);
}

module.exports = { testFinalFunctionality };
