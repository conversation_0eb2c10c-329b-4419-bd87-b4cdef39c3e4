const { Client } = require('pg');

const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function debugPriceStructure() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔍 分析 Medusa v2 价格结构...\n');

    // 1. 检查价格相关表
    console.log('📋 价格相关表:');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE '%price%' 
      ORDER BY table_name
    `);
    
    tablesResult.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });

    // 2. 检查 price_set 表结构
    console.log('\n🏗️ price_set 表结构:');
    const priceSetStructure = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'price_set' 
      ORDER BY ordinal_position
    `);
    
    priceSetStructure.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // 3. 检查 price 表结构
    console.log('\n💰 price 表结构:');
    const priceStructure = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'price' 
      ORDER BY ordinal_position
    `);
    
    priceStructure.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // 4. 检查 price_rule 表结构和数据
    console.log('\n📏 price_rule 表结构:');
    const priceRuleStructure = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'price_rule'
      ORDER BY ordinal_position
    `);

    priceRuleStructure.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    console.log('\n📏 price_rule 表数据:');
    const priceRulesResult = await client.query(`
      SELECT *
      FROM price_rule
      WHERE deleted_at IS NULL
      LIMIT 10
    `);
    
    if (priceRulesResult.rows.length === 0) {
      console.log('  ❌ 没有找到价格规则！这可能是问题的根源。');
    } else {
      priceRulesResult.rows.forEach(row => {
        console.log(`  - ${JSON.stringify(row)}`);
      });
    }

    // 5. 检查 region 和 currency 配置
    console.log('\n🌍 Region 和 Currency 配置:');
    const regionResult = await client.query(`
      SELECT r.id, r.name, r.currency_code, c.name as currency_name
      FROM region r
      LEFT JOIN currency c ON r.currency_code = c.code
      WHERE r.deleted_at IS NULL
    `);
    
    regionResult.rows.forEach(row => {
      console.log(`  - ${row.name} (${row.id}): ${row.currency_code} - ${row.currency_name || 'Unknown'}`);
    });

    // 6. 检查具体变体的价格链路
    console.log('\n🔗 变体价格链路分析:');
    const variantId = 'variant_1755366640268_ae3h859og';
    const priceChainResult = await client.query(`
      SELECT 
        pv.id as variant_id,
        pv.title as variant_title,
        pvps.price_set_id,
        ps.id as price_set_check,
        p.id as price_id,
        p.amount,
        p.currency_code,
        p.rules_count,
        p.raw_amount
      FROM product_variant pv
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id AND pvps.deleted_at IS NULL
      LEFT JOIN price_set ps ON pvps.price_set_id = ps.id AND ps.deleted_at IS NULL
      LEFT JOIN price p ON ps.id = p.price_set_id AND p.deleted_at IS NULL
      WHERE pv.id = $1
    `, [variantId]);
    
    if (priceChainResult.rows.length > 0) {
      const chain = priceChainResult.rows[0];
      console.log(`  变体: ${chain.variant_title}`);
      console.log(`  Price Set ID: ${chain.price_set_id || '❌ 缺失'}`);
      console.log(`  Price Set 存在: ${chain.price_set_check ? '✅' : '❌'}`);
      console.log(`  Price ID: ${chain.price_id || '❌ 缺失'}`);
      console.log(`  金额: ${chain.amount || '❌ 缺失'}`);
      console.log(`  货币: ${chain.currency_code || '❌ 缺失'}`);
      console.log(`  规则数量: ${chain.rules_count || 0}`);
    } else {
      console.log(`  ❌ 变体 ${variantId} 不存在`);
    }

    // 7. 检查 Medusa 配置表
    console.log('\n⚙️ Medusa 配置检查:');
    
    // 检查是否有 price_list 表
    const priceListCheck = await client.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_name = 'price_list'
    `);
    
    if (priceListCheck.rows[0].count > 0) {
      const priceListResult = await client.query(`
        SELECT id, name, type, status 
        FROM price_list 
        WHERE deleted_at IS NULL 
        LIMIT 5
      `);
      
      console.log(`  Price Lists: ${priceListResult.rows.length} 个`);
      priceListResult.rows.forEach(row => {
        console.log(`    - ${row.name} (${row.type}, ${row.status})`);
      });
    } else {
      console.log('  ❌ 没有 price_list 表');
    }

    // 8. 生成诊断报告
    console.log('\n🩺 诊断报告:');
    console.log('='.repeat(50));
    
    if (priceRulesResult.rows.length === 0) {
      console.log('❌ 主要问题: 缺少价格规则 (price_rule)');
      console.log('💡 解决方案: 需要创建默认的价格规则');
    }
    
    if (regionResult.rows.length === 0) {
      console.log('❌ 问题: 缺少 region 配置');
    } else {
      console.log(`✅ Region 配置正常: ${regionResult.rows.length} 个区域`);
    }

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    await client.end();
  }
}

if (require.main === module) {
  debugPriceStructure().catch(console.error);
}

module.exports = { debugPriceStructure };
