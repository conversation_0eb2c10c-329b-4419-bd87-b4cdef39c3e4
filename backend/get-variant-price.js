const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function getVariantPrice(variantId) {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    
    const result = await client.query(`
      SELECT 
        pv.id as variant_id,
        pv.title as variant_title,
        p.amount,
        p.currency_code,
        prod.title as product_title
      FROM product_variant pv 
      INNER JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id 
      INNER JOIN price p ON pvps.price_set_id = p.price_set_id 
      INNER JOIN product prod ON pv.product_id = prod.id
      WHERE pv.id = $1 
      AND pv.deleted_at IS NULL 
      AND pvps.deleted_at IS NULL 
      AND p.deleted_at IS NULL
    `, [variantId]);
    
    if (result.rows.length > 0) {
      const row = result.rows[0];
      return {
        variant_id: row.variant_id,
        variant_title: row.variant_title,
        product_title: row.product_title,
        amount: row.amount,
        currency_code: row.currency_code,
        formatted_price: `$${(row.amount / 100).toFixed(2)}`
      };
    }
    
    return null;
  } catch (error) {
    console.error('获取变体价格失败:', error);
    return null;
  } finally {
    await client.end();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const variantId = process.argv[2];
  if (!variantId) {
    console.log('用法: node get-variant-price.js <variant_id>');
    process.exit(1);
  }
  
  getVariantPrice(variantId).then(result => {
    if (result) {
      console.log('变体价格信息:');
      console.log(`  产品: ${result.product_title}`);
      console.log(`  变体: ${result.variant_title}`);
      console.log(`  价格: ${result.formatted_price}`);
      console.log(`  货币: ${result.currency_code}`);
      console.log(`  原始金额: ${result.amount} cents`);
    } else {
      console.log('未找到价格信息');
    }
  }).catch(console.error);
}

module.exports = { getVariantPrice };
