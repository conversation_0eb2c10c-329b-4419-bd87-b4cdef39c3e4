{"name": "medusa-starter-default", "version": "0.0.1", "description": "A starter for Medusa projects.", "author": "Medusa (https://medusajs.com)", "license": "MIT", "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "scripts": {"build": "medusa build", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "dev": "medusa develop", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit"}, "dependencies": {"@medusajs/admin-sdk": "2.0.0", "@medusajs/cli": "2.0.0", "@medusajs/framework": "2.0.0", "@medusajs/medusa": "2.0.0", "@mikro-orm/core": "5.9.7", "@mikro-orm/knex": "5.9.7", "@mikro-orm/migrations": "5.9.7", "@mikro-orm/postgresql": "5.9.7", "@react-email/components": "^0.0.25", "@react-email/render": "^1.0.1", "@sanity/client": "^6.29.1", "awilix": "^8.0.1", "cheerio": "^1.1.2", "node-cron": "^4.2.1", "node-fetch": "^3.3.2", "pg": "^8.16.3", "resend": "^4.0.0"}, "devDependencies": {"@medusajs/test-utils": "2.0.0", "@mikro-orm/cli": "5.9.7", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/jest": "^29.5.13", "@types/node": "^20.0.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "jest": "^29.7.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11"}, "engines": {"node": ">=20"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}