const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'medusa_dev',
  user: 'postgres',
  password: '', // 通常本地开发没有密码
};

// 中文 handle 到英文 handle 的映射
const handleMappings = {
  '威武神龙雕刻-1': 'mighty-dragon-carving',
  '祥瑞凤凰摆件-2': 'auspicious-phoenix-ornament',
  '福禄寿三星-3': 'three-stars-of-fortune',
  '观音菩萨像-4': 'guanyin-buddha-statue',
  '梅兰竹菊四君子-5': 'four-gentlemen-carving',
  '荷花莲蓬雅韵-6': 'lotus-pond-elegance',
  '现代抽象艺术品-7': 'modern-abstract-art',
  '简约线条雕塑-8': 'minimalist-line-sculpture',
  '创意动物造型-9': 'creative-animal-sculpture',
  '几何拼接艺术-10': 'geometric-mosaic-art',
  '精美茶具套装-11': 'exquisite-tea-set',
  '文房四宝盒-12': 'four-treasures-study-box',
  '香薰炉座-13': 'incense-burner-stand',
  '珠宝首饰盒-14': 'jewelry-box',
  '古典花瓶架-15': 'classical-vase-stand',
  '禅意水景摆件-16': 'zen-water-landscape',
  '山水意境雕刻-17': 'landscape-mood-carving',
  '书法艺术屏风-18': 'calligraphy-art-screen',
  '现代灯具底座-19': 'modern-lamp-base',
  '艺术收纳盒-20': 'artistic-storage-box'
};

async function fixProductHandlesDB() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔌 连接数据库...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 查询所有需要更新的产品
    console.log('🔍 查找需要更新的产品...');
    const chineseHandles = Object.keys(handleMappings);
    const placeholders = chineseHandles.map((_, index) => `$${index + 1}`).join(',');
    
    const selectQuery = `
      SELECT id, title, handle 
      FROM product 
      WHERE handle IN (${placeholders})
    `;
    
    const selectResult = await client.query(selectQuery, chineseHandles);
    const productsToUpdate = selectResult.rows;
    
    console.log(`📦 找到 ${productsToUpdate.length} 个需要更新的产品:`);
    productsToUpdate.forEach(product => {
      console.log(`  - ${product.title} (${product.handle}) -> ${handleMappings[product.handle]}`);
    });

    if (productsToUpdate.length === 0) {
      console.log('✅ 所有产品的 handle 都已经是英文，无需更新');
      return;
    }

    // 2. 批量更新产品 handle
    console.log('\n🔄 开始更新产品 handle...');
    let successCount = 0;
    let failCount = 0;

    for (const product of productsToUpdate) {
      const newHandle = handleMappings[product.handle];
      
      try {
        // 检查新 handle 是否已存在
        const checkQuery = 'SELECT id FROM product WHERE handle = $1 AND id != $2';
        const checkResult = await client.query(checkQuery, [newHandle, product.id]);
        
        if (checkResult.rows.length > 0) {
          console.log(`⚠️  Handle "${newHandle}" 已存在，跳过产品: ${product.title}`);
          failCount++;
          continue;
        }

        // 更新产品 handle
        const updateQuery = 'UPDATE product SET handle = $1 WHERE id = $2';
        await client.query(updateQuery, [newHandle, product.id]);
        
        console.log(`✅ 成功更新: ${product.title} -> ${newHandle}`);
        successCount++;
        
      } catch (error) {
        console.error(`❌ 更新失败 ${product.title}:`, error.message);
        failCount++;
      }
    }

    console.log('\n📊 更新完成统计:');
    console.log(`✅ 成功: ${successCount} 个产品`);
    console.log(`❌ 失败: ${failCount} 个产品`);
    console.log(`📦 总计: ${productsToUpdate.length} 个产品`);
    
    if (successCount > 0) {
      console.log('\n🎉 产品 handle 修复完成！现在所有产品都有英文 handle，可以正常访问了。');
      console.log('💡 建议重启 Medusa 服务以确保更改生效。');
    }

  } catch (error) {
    console.error('❌ 数据库操作失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixProductHandlesDB().catch(console.error);
}

module.exports = {
  fixProductHandlesDB,
  handleMappings
};
