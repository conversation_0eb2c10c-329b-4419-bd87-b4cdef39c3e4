async function testPriceFields() {
  const fetch = (await import('node-fetch')).default;
  
  console.log('🧪 测试不同的价格字段...\n');

  const baseUrl = 'http://localhost:9000';
  const publishableKey = 'pk_f3bf2b6ad7a6898050f237e15b93dea6dac071ae49c29bfbe5e9b1dad90ee2b1';
  const regionId = 'reg_mecot5lgy79vtq0q8';
  const productHandle = 'tomo-carved-wooden-brown-bear';

  const testFields = [
    '*variants.calculated_price',
    '*variants.prices',
    '*variants.price_set',
    '*variants.price_set.prices',
    '+variants.price_set.prices',
    '*variants.*',
    '+variants.*',
    '*variants.calculated_price,+variants.prices',
    '*variants.calculated_price,+variants.price_set',
    '*variants.calculated_price,+variants.price_set.prices',
  ];

  for (const fields of testFields) {
    try {
      console.log(`🔍 测试字段: ${fields}`);
      
      const response = await fetch(
        `${baseUrl}/store/products?handle=${productHandle}&region_id=${regionId}&fields=${encodeURIComponent(fields)}`,
        {
          headers: {
            'x-publishable-api-key': publishableKey,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        console.log(`   ❌ 请求失败: ${response.status}`);
        continue;
      }

      const data = await response.json();
      const product = data.products[0];
      
      if (!product || !product.variants || product.variants.length === 0) {
        console.log('   ❌ 没有产品或变体');
        continue;
      }

      const variant = product.variants[0];
      console.log(`   ✅ 响应成功`);
      console.log(`      calculated_price: ${variant.calculated_price ? 'exists' : 'null'}`);
      console.log(`      prices: ${variant.prices ? 'exists' : 'undefined'}`);
      console.log(`      price_set: ${variant.price_set ? 'exists' : 'undefined'}`);
      
      if (variant.calculated_price) {
        console.log(`      calculated_amount: ${variant.calculated_price.calculated_amount}`);
      }
      
      if (variant.prices) {
        console.log(`      prices length: ${variant.prices.length}`);
        if (variant.prices.length > 0) {
          console.log(`      first price: ${variant.prices[0].amount} ${variant.prices[0].currency_code}`);
        }
      }
      
      if (variant.price_set) {
        console.log(`      price_set id: ${variant.price_set.id}`);
        if (variant.price_set.prices) {
          console.log(`      price_set.prices length: ${variant.price_set.prices.length}`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ 错误: ${error.message}`);
    }
    
    console.log('');
  }

  // 测试特定的价格计算上下文
  console.log('🎯 测试带上下文的价格计算...');
  
  try {
    const contextResponse = await fetch(
      `${baseUrl}/store/products?handle=${productHandle}&region_id=${regionId}&fields=*variants.calculated_price&currency_code=usd&customer_id=&sales_channel_id=`,
      {
        headers: {
          'x-publishable-api-key': publishableKey,
          'Content-Type': 'application/json',
        },
      }
    );

    if (contextResponse.ok) {
      const contextData = await contextResponse.json();
      const contextVariant = contextData.products[0]?.variants[0];
      console.log(`   ✅ 带上下文的 calculated_price: ${contextVariant?.calculated_price ? 'exists' : 'null'}`);
      if (contextVariant?.calculated_price) {
        console.log(`      calculated_amount: ${contextVariant.calculated_price.calculated_amount}`);
      }
    } else {
      console.log(`   ❌ 带上下文的请求失败: ${contextResponse.status}`);
    }
  } catch (error) {
    console.log(`   ❌ 带上下文的请求错误: ${error.message}`);
  }
}

if (require.main === module) {
  testPriceFields().catch(console.error);
}

module.exports = { testPriceFields };
