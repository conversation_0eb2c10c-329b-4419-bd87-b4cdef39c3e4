const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function fixPriceCalculation() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔧 开始修复价格计算...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 检查当前价格规则
    console.log('\n📋 检查价格规则...');
    const priceRulesResult = await client.query(`
      SELECT * FROM price_rule WHERE deleted_at IS NULL
    `);
    console.log(`当前价格规则数量: ${priceRulesResult.rows.length}`);
    
    // 2. 检查价格偏好设置
    console.log('\n⚙️ 检查价格偏好...');
    const pricePreferencesResult = await client.query(`
      SELECT * FROM price_preference WHERE deleted_at IS NULL
    `);
    console.log(`当前价格偏好数量: ${pricePreferencesResult.rows.length}`);
    pricePreferencesResult.rows.forEach(pref => {
      console.log(`  - ${pref.attribute}: ${pref.value} (is_tax_inclusive: ${pref.is_tax_inclusive})`);
    });
    
    // 3. 检查区域配置
    console.log('\n🌍 检查区域配置...');
    const regionsResult = await client.query(`
      SELECT id, name, currency_code, automatic_taxes FROM region WHERE deleted_at IS NULL
    `);
    console.log(`区域数量: ${regionsResult.rows.length}`);
    regionsResult.rows.forEach(region => {
      console.log(`  - ${region.name} (${region.id}): ${region.currency_code}, 自动税收: ${region.automatic_taxes}`);
    });
    
    // 4. 检查价格集和价格的关联
    console.log('\n💰 检查价格数据...');
    const priceDataResult = await client.query(`
      SELECT 
        ps.id as price_set_id,
        p.id as price_id,
        p.currency_code,
        p.amount,
        p.rules_count,
        pvps.variant_id
      FROM price_set ps
      INNER JOIN price p ON ps.id = p.price_set_id
      INNER JOIN product_variant_price_set pvps ON ps.id = pvps.price_set_id
      WHERE ps.deleted_at IS NULL 
      AND p.deleted_at IS NULL 
      AND pvps.deleted_at IS NULL
      LIMIT 5
    `);
    
    console.log(`价格数据条目: ${priceDataResult.rows.length}`);
    priceDataResult.rows.forEach(price => {
      console.log(`  - 变体 ${price.variant_id}: ${price.currency_code} ${price.amount/100} (规则数: ${price.rules_count})`);
    });
    
    // 5. 如果没有价格规则，创建默认规则
    if (priceRulesResult.rows.length === 0) {
      console.log('\n🆕 创建默认价格规则...');
      
      // 为每个区域创建价格规则
      for (const region of regionsResult.rows) {
        const ruleId = `prule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        await client.query(`
          INSERT INTO price_rule (id, rule_type_id, value, created_at, updated_at)
          VALUES ($1, $2, $3, NOW(), NOW())
        `, [ruleId, 'region_id', region.id]);
        
        console.log(`  ✅ 创建区域规则: ${region.name} (${ruleId})`);
      }
    }
    
    // 6. 检查并修复价格规则计数
    console.log('\n🔄 更新价格规则计数...');
    const updateRulesCountResult = await client.query(`
      UPDATE price
      SET rules_count = 0
      WHERE deleted_at IS NULL
    `);
    
    console.log(`✅ 更新了 ${updateRulesCountResult.rowCount} 个价格的规则计数`);
    
    // 7. 验证修复结果
    console.log('\n✅ 验证修复结果...');
    const verificationResult = await client.query(`
      SELECT 
        pv.id as variant_id,
        pv.title as variant_title,
        p.currency_code,
        p.amount,
        p.rules_count
      FROM product_variant pv
      INNER JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      INNER JOIN price p ON pvps.price_set_id = p.price_set_id
      INNER JOIN product prod ON pv.product_id = prod.id
      WHERE pv.deleted_at IS NULL 
      AND pvps.deleted_at IS NULL 
      AND p.deleted_at IS NULL
      AND prod.deleted_at IS NULL
      AND (prod.title LIKE '%Carved%' OR prod.title LIKE '%Wooden%')
      LIMIT 3
    `);
    
    console.log(`验证结果 - 找到 ${verificationResult.rows.length} 个有价格的变体:`);
    verificationResult.rows.forEach(variant => {
      console.log(`  - ${variant.variant_title}: ${variant.currency_code} ${variant.amount/100} (规则: ${variant.rules_count})`);
    });
    
    console.log('\n🎉 价格计算修复完成！');
    console.log('💡 建议重启 Medusa 服务以应用更改');

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixPriceCalculation().catch(console.error);
}

module.exports = { fixPriceCalculation };
