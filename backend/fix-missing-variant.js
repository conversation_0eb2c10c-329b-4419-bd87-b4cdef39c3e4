const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function createVariantForProduct(client, productId, productTitle) {
  try {
    const variantId = `variant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const sku = productTitle.toUpperCase().replace(/[^A-Z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
    
    // 创建产品变体
    await client.query(`
      INSERT INTO product_variant (
        id, title, sku, product_id, allow_backorder, manage_inventory, 
        variant_rank, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
    `, [variantId, 'Default', sku, productId, false, true, 0]);
    
    console.log(`✅ 创建变体: ${variantId} for ${productTitle}`);
    return variantId;
  } catch (error) {
    console.error(`创建变体失败:`, error.message);
    return null;
  }
}

async function createPriceSet(client, variantId, price) {
  try {
    const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建 price_set
    await client.query(`
      INSERT INTO price_set (id, created_at, updated_at)
      VALUES ($1, NOW(), NOW())
    `, [priceSetId]);
    
    // 创建 price
    const priceId = `price_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    await client.query(`
      INSERT INTO price (id, price_set_id, currency_code, amount, raw_amount, rules_count, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
    `, [priceId, priceSetId, 'usd', price, JSON.stringify({value: price}), 0]);
    
    // 关联 variant 到 price_set
    const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    await client.query(`
      INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
    `, [linkId, variantId, priceSetId]);
    
    console.log(`✅ 创建价格: $${(price / 100).toFixed(2)}`);
    return priceSetId;
  } catch (error) {
    console.error(`创建价格失败:`, error.message);
    return null;
  }
}

async function createProductOption(client, productId, optionTitle, values) {
  try {
    const optionId = `opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建 product_option
    await client.query(`
      INSERT INTO product_option (id, title, product_id, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
    `, [optionId, optionTitle, productId]);
    
    // 创建 product_option_value
    for (const value of values) {
      const valueId = `optval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await client.query(`
        INSERT INTO product_option_value (id, value, option_id, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
      `, [valueId, value.value, optionId]);
    }
    
    console.log(`✅ 创建选项: ${optionTitle}`);
    return optionId;
  } catch (error) {
    console.error(`创建产品选项失败:`, error.message);
    return null;
  }
}

async function fixMissingVariant() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 开始修复缺少变体的产品...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 获取没有变体的产品
    const productsResult = await client.query(`
      SELECT p.id, p.title 
      FROM product p 
      WHERE p.deleted_at IS NULL 
      AND (p.title LIKE '%Carved%' OR p.title LIKE '%Wooden%') 
      AND p.id NOT IN (
        SELECT DISTINCT pv.product_id 
        FROM product_variant pv 
        WHERE pv.deleted_at IS NULL
      )
    `);
    
    const products = productsResult.rows;
    console.log(`📦 找到 ${products.length} 个需要创建变体的产品`);

    for (const product of products) {
      console.log(`🔧 处理产品: ${product.title}`);
      
      // 1. 创建默认变体
      const variantId = await createVariantForProduct(client, product.id, product.title);
      if (!variantId) continue;
      
      // 2. 创建价格
      const price = Math.round((Math.random() * 30 + 20) * 100); // $20-$50
      await createPriceSet(client, variantId, price);
      
      // 3. 创建产品选项
      const sizeOptions = [
        { name: 'Small', value: 'small' },
        { name: 'Medium', value: 'medium' },
        { name: 'Large', value: 'large' }
      ];
      
      const colorOptions = [
        { name: 'Natural', value: 'natural' },
        { name: 'Dark Brown', value: 'dark-brown' },
        { name: 'Light Oak', value: 'light-oak' }
      ];
      
      await createProductOption(client, product.id, 'Size', sizeOptions);
      await createProductOption(client, product.id, 'Color', colorOptions);
      
      // 4. 更新产品描述
      const description = "Handcrafted with Natural Wood\n\nHandcrafted\nCute, unique and adorable\nWonderful gift for kids and loved ones\nAmazing table decoration\nMindful Moments in Solid Wood";
      const metadata = {
        ingredients: "Materials: Premium hardwood (Oak/Maple)\nFinish: Natural wood oil\nCraftsmanship: Hand-carved\nOrigin: Artisan workshop",
        shipping: "Free shipping on orders over $50\nStandard delivery: 5-7 business days\nExpress delivery: 2-3 business days\nInternational shipping available\nCarefully packaged for protection",
        care_instructions: "Clean with dry cloth only. Avoid direct sunlight and moisture.",
        warranty: "1 year craftsmanship warranty"
      };
      
      await client.query(`
        UPDATE product 
        SET description = $1, metadata = $2, updated_at = NOW()
        WHERE id = $3
      `, [description, JSON.stringify(metadata), product.id]);
      
      console.log(`✅ 更新产品描述和元数据`);
    }

    console.log('\n🎉 缺少变体的产品修复完成！');

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixMissingVariant().catch(console.error);
}

module.exports = { fixMissingVariant };
