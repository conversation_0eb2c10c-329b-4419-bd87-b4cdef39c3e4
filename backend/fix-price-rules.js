const { Client } = require('pg');

const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

function generateId(prefix) {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${random}`;
}

async function fixPriceRules() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔧 开始修复价格规则...\n');

    // 1. 获取 US 区域 ID
    const usRegionResult = await client.query(`
      SELECT rc.region_id, r.name as region_name, r.currency_code
      FROM region_country rc
      INNER JOIN region r ON rc.region_id = r.id
      WHERE rc.iso_2 = 'us' AND r.deleted_at IS NULL
    `);
    
    if (usRegionResult.rows.length === 0) {
      console.error('❌ 找不到 US 区域映射');
      return;
    }
    
    const usRegion = usRegionResult.rows[0];
    console.log(`🇺🇸 US 区域: ${usRegion.region_name} (${usRegion.region_id})`);
    console.log(`💰 货币: ${usRegion.currency_code}\n`);

    // 2. 获取所有 Mokuomo 产品的价格（没有 US 区域规则的）
    console.log('🔍 查找需要修复的价格...');
    const pricesNeedingRulesResult = await client.query(`
      SELECT DISTINCT
        p.id as price_id,
        p.amount,
        p.currency_code,
        pv.id as variant_id,
        pv.title as variant_title,
        prod.title as product_title
      FROM product_variant pv
      INNER JOIN product prod ON pv.product_id = prod.id
      INNER JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      INNER JOIN price p ON pvps.price_set_id = p.price_set_id
      LEFT JOIN price_rule pr ON p.id = pr.price_id AND pr.attribute = 'region_id' AND pr.value = $1
      WHERE pv.deleted_at IS NULL 
      AND pvps.deleted_at IS NULL 
      AND p.deleted_at IS NULL
      AND prod.deleted_at IS NULL
      AND (prod.title LIKE '%Carved%' OR prod.title LIKE '%Wooden%')
      AND pr.id IS NULL  -- 没有 US 区域规则的价格
      ORDER BY prod.title, pv.title
    `, [usRegion.region_id]);
    
    console.log(`找到 ${pricesNeedingRulesResult.rows.length} 个需要修复的价格\n`);
    
    if (pricesNeedingRulesResult.rows.length === 0) {
      console.log('✅ 所有价格都已有 US 区域规则');
      return;
    }

    // 3. 为每个价格创建 US 区域规则
    console.log('🛠️ 创建价格规则...');
    let createdCount = 0;
    let errorCount = 0;
    
    for (const priceData of pricesNeedingRulesResult.rows) {
      try {
        const ruleId = generateId('prule');
        
        await client.query(`
          INSERT INTO price_rule (
            id, 
            attribute, 
            value, 
            priority, 
            price_id, 
            created_at, 
            updated_at
          ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        `, [
          ruleId,
          'region_id',
          usRegion.region_id,
          0, // 默认优先级
          priceData.price_id
        ]);
        
        console.log(`  ✅ ${priceData.product_title} - ${priceData.variant_title}: $${(priceData.amount / 100).toFixed(2)}`);
        createdCount++;
        
        // 添加小延迟避免 ID 冲突
        await new Promise(resolve => setTimeout(resolve, 5));
        
      } catch (error) {
        console.error(`  ❌ 失败: ${priceData.product_title} - ${error.message}`);
        errorCount++;
      }
    }

    // 4. 验证修复结果
    console.log('\n🔍 验证修复结果...');
    const verificationResult = await client.query(`
      SELECT 
        COUNT(*) as total_prices,
        COUNT(pr.id) as prices_with_us_rules
      FROM product_variant pv
      INNER JOIN product prod ON pv.product_id = prod.id
      INNER JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      INNER JOIN price p ON pvps.price_set_id = p.price_set_id
      LEFT JOIN price_rule pr ON p.id = pr.price_id AND pr.attribute = 'region_id' AND pr.value = $1
      WHERE pv.deleted_at IS NULL 
      AND pvps.deleted_at IS NULL 
      AND p.deleted_at IS NULL
      AND prod.deleted_at IS NULL
      AND (prod.title LIKE '%Carved%' OR prod.title LIKE '%Wooden%')
    `, [usRegion.region_id]);
    
    const verification = verificationResult.rows[0];
    
    console.log('\n📊 修复统计:');
    console.log('='.repeat(50));
    console.log(`总价格数: ${verification.total_prices}`);
    console.log(`有 US 区域规则的价格: ${verification.prices_with_us_rules}`);
    console.log(`成功创建规则: ${createdCount}`);
    console.log(`创建失败: ${errorCount}`);
    
    if (verification.total_prices === verification.prices_with_us_rules) {
      console.log('\n🎉 所有价格都已有 US 区域规则！');
      console.log('💡 现在前端应该能正确显示价格了');
      console.log('🔄 建议重启 Medusa 服务以应用更改');
    } else {
      const missing = verification.total_prices - verification.prices_with_us_rules;
      console.log(`\n⚠️  还有 ${missing} 个价格缺少 US 区域规则`);
    }

    // 5. 测试特定变体
    console.log('\n🧪 测试特定变体...');
    const testVariantId = 'variant_1755366640268_ae3h859og';
    const testResult = await client.query(`
      SELECT 
        p.id as price_id,
        p.amount,
        p.currency_code,
        pr.id as rule_id,
        pr.attribute,
        pr.value
      FROM product_variant pv
      INNER JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      INNER JOIN price p ON pvps.price_set_id = p.price_set_id
      INNER JOIN price_rule pr ON p.id = pr.price_id
      WHERE pv.id = $1 
      AND pr.attribute = 'region_id' 
      AND pr.value = $2
      AND pv.deleted_at IS NULL 
      AND pvps.deleted_at IS NULL 
      AND p.deleted_at IS NULL
    `, [testVariantId, usRegion.region_id]);
    
    if (testResult.rows.length > 0) {
      const test = testResult.rows[0];
      console.log(`✅ 变体 ${testVariantId} 现在有 US 区域价格规则`);
      console.log(`   价格: $${(test.amount / 100).toFixed(2)} ${test.currency_code}`);
      console.log(`   规则: ${test.rule_id}`);
    } else {
      console.log(`❌ 变体 ${testVariantId} 仍然没有 US 区域价格规则`);
    }

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await client.end();
    console.log('\n🔌 数据库连接已关闭');
  }
}

if (require.main === module) {
  fixPriceRules().catch(console.error);
}

module.exports = { fixPriceRules };
