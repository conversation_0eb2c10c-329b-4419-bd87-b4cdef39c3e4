const { Client } = require('pg');

const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function checkRegionMapping() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔍 检查 Region 映射和价格规则...\n');

    // 1. 检查所有 regions
    console.log('🌍 所有 Regions:');
    const regionsResult = await client.query(`
      SELECT r.id, r.name, r.currency_code, r.created_at
      FROM region r
      WHERE r.deleted_at IS NULL
      ORDER BY r.created_at
    `);
    
    regionsResult.rows.forEach(row => {
      console.log(`  - ${row.name} (${row.id}): ${row.currency_code}`);
    });

    // 2. 检查国家到区域的映射
    console.log('\n🗺️ 国家到区域映射:');
    const countryMappingResult = await client.query(`
      SELECT rc.iso_2, rc.display_name, rc.region_id, r.name as region_name
      FROM region_country rc
      LEFT JOIN region r ON rc.region_id = r.id
      WHERE r.deleted_at IS NULL OR r.deleted_at IS NULL
      ORDER BY rc.iso_2
    `);
    
    countryMappingResult.rows.forEach(row => {
      console.log(`  - ${row.iso_2} (${row.display_name}): ${row.region_name || '❌ 无区域'} (${row.region_id || 'null'})`);
    });

    // 3. 检查 US 的映射
    console.log('\n🇺🇸 US 区域映射:');
    const usRegionResult = await client.query(`
      SELECT rc.iso_2, rc.display_name, rc.region_id, r.name as region_name, r.currency_code
      FROM region_country rc
      LEFT JOIN region r ON rc.region_id = r.id
      WHERE rc.iso_2 = 'us'
    `);
    
    if (usRegionResult.rows.length > 0) {
      const usMapping = usRegionResult.rows[0];
      console.log(`  US 映射到: ${usMapping.region_name} (${usMapping.region_id})`);
      console.log(`  货币: ${usMapping.currency_code}`);
      
      // 检查这个区域的价格规则
      if (usMapping.region_id) {
        console.log('\n💰 US 区域的价格规则:');
        const usPriceRulesResult = await client.query(`
          SELECT pr.id, pr.attribute, pr.value, pr.priority, p.amount, p.currency_code
          FROM price_rule pr
          INNER JOIN price p ON pr.price_id = p.id
          WHERE pr.attribute = 'region_id' AND pr.value = $1 AND pr.deleted_at IS NULL
          ORDER BY pr.priority DESC
        `, [usMapping.region_id]);
        
        if (usPriceRulesResult.rows.length > 0) {
          usPriceRulesResult.rows.forEach(row => {
            console.log(`    - 规则 ${row.id}: $${(row.amount / 100).toFixed(2)} ${row.currency_code}`);
          });
        } else {
          console.log('    ❌ 没有找到 US 区域的价格规则');
        }
      }
    } else {
      console.log('  ❌ US 没有映射到任何区域');
    }

    // 4. 检查所有价格规则
    console.log('\n📏 所有价格规则:');
    const allPriceRulesResult = await client.query(`
      SELECT 
        pr.id, 
        pr.attribute, 
        pr.value, 
        pr.priority, 
        p.amount, 
        p.currency_code,
        r.name as region_name
      FROM price_rule pr
      INNER JOIN price p ON pr.price_id = p.id
      LEFT JOIN region r ON pr.value = r.id
      WHERE pr.deleted_at IS NULL
      ORDER BY pr.attribute, pr.priority DESC
    `);
    
    if (allPriceRulesResult.rows.length > 0) {
      allPriceRulesResult.rows.forEach(row => {
        console.log(`  - ${row.attribute}=${row.value} (${row.region_name || 'Unknown'}): $${(row.amount / 100).toFixed(2)} ${row.currency_code}`);
      });
    } else {
      console.log('  ❌ 没有找到任何价格规则');
    }

    // 5. 检查我们的产品变体是否有针对正确区域的价格
    console.log('\n🔍 检查产品变体的区域价格:');
    const variantId = 'variant_1755366640268_ae3h859og';
    
    // 获取这个变体的所有价格
    const variantPricesResult = await client.query(`
      SELECT 
        p.id as price_id,
        p.amount,
        p.currency_code,
        pr.attribute,
        pr.value as rule_value,
        r.name as region_name
      FROM product_variant pv
      INNER JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      INNER JOIN price p ON pvps.price_set_id = p.price_set_id
      LEFT JOIN price_rule pr ON p.id = pr.price_id AND pr.attribute = 'region_id'
      LEFT JOIN region r ON pr.value = r.id
      WHERE pv.id = $1 AND pv.deleted_at IS NULL AND pvps.deleted_at IS NULL AND p.deleted_at IS NULL
    `, [variantId]);
    
    if (variantPricesResult.rows.length > 0) {
      variantPricesResult.rows.forEach(row => {
        console.log(`  - 价格 ${row.price_id}: $${(row.amount / 100).toFixed(2)} ${row.currency_code}`);
        if (row.region_name) {
          console.log(`    关联区域: ${row.region_name} (${row.rule_value})`);
        } else {
          console.log(`    ❌ 没有区域规则`);
        }
      });
    } else {
      console.log(`  ❌ 变体 ${variantId} 没有价格数据`);
    }

    // 6. 生成修复建议
    console.log('\n💡 修复建议:');
    console.log('='.repeat(50));
    
    const usRegion = usRegionResult.rows[0];
    if (!usRegion || !usRegion.region_id) {
      console.log('1. ❌ 需要为 US 创建或修复区域映射');
    } else {
      console.log(`1. ✅ US 已映射到区域: ${usRegion.region_name}`);
      
      // 检查是否有针对 US 区域的价格规则
      const usPriceRulesCount = await client.query(`
        SELECT COUNT(*) as count
        FROM price_rule pr
        WHERE pr.attribute = 'region_id' AND pr.value = $1 AND pr.deleted_at IS NULL
      `, [usRegion.region_id]);
      
      if (usPriceRulesCount.rows[0].count === '0') {
        console.log('2. ❌ 需要为所有产品变体创建 US 区域的价格规则');
        console.log('   建议: 为每个变体的价格添加 region_id 规则');
      } else {
        console.log(`2. ✅ 已有 ${usPriceRulesCount.rows[0].count} 个 US 区域价格规则`);
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await client.end();
  }
}

if (require.main === module) {
  checkRegionMapping().catch(console.error);
}

module.exports = { checkRegionMapping };
