STORE_CORS=http://localhost:8000,https://docs.medusajs.com
ADMIN_CORS=http://localhost:7000,http://localhost:7001,https://docs.medusajs.com
AUTH_CORS=http://localhost:7000,http://localhost:7001,https://docs.medusajs.com
REDIS_URL=redis://localhost:6379
JWT_SECRET=supersecret
COOKIE_SECRET=supersecret
DATABASE_URL=
DB_NAME=
POSTGRES_URL=
BACKEND_URL="" # The deployed URL and for development just use "http://localhost:9000"

## To handle images and files we use the S3 file module

S3_FILE_URL=""
S3_ACCESS_KEY_ID=""
S3_SECRET_ACCESS_KEY=""
S3_REGION=""
S3_BUCKET=""
S3_ENDPOINT=""

## Sanity - Data sync

SANITY_API_TOKEN="sk7e8ed8JSEHUu9T7w4t5iZiwwBztstYhqEiSb1d5hprDOy5W2ngaMukPrAhIdcRTaV2b9MXwZOZ6iZqPV7yJ5EJj1jTB5DwNHZoTjtmuxOIQDsatycDeJZPTH8iTdvs5dcPUdSm1r91Q23zmFv7dAe3IjG89X2x8LiNAmWeqxPa32ThoHrQ"
SANITY_PROJECT_ID="qqvccmla"
SANITY_ORGANIZATION_ID="ohsoWcUsW"

## Emails
MEDUSA_PUBLISHABLE_KEY="" # Needed for the admin extensions, you can get this from the deployed admin
RESEND_API_KEY="" # resend.dev