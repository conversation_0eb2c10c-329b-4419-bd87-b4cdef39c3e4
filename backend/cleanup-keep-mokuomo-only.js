const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

// Mokuomo 分类 handles（要保留的）
const mokuomoCategories = [
  'combos',
  'best-sellers', 
  'wildlife',
  'bears',
  'cats',
  'dogs',
  'fun-finds',
  'birds'
];

async function cleanupDatabase() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 开始清理数据库，仅保留 Mokuomo 数据...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 获取要保留的 Mokuomo 分类 ID
    const mokuomoCategoriesResult = await client.query(`
      SELECT id, name, handle 
      FROM product_category 
      WHERE handle = ANY($1)
      AND deleted_at IS NULL
    `, [mokuomoCategories]);
    
    const mokuomoCategoryIds = mokuomoCategoriesResult.rows.map(row => row.id);
    console.log(`📁 找到 ${mokuomoCategoryIds.length} 个 Mokuomo 分类:`, 
      mokuomoCategoriesResult.rows.map(r => r.name));

    // 2. 获取要保留的 Mokuomo 产品（关联到 Mokuomo 分类的产品）
    const mokuomoProductsResult = await client.query(`
      SELECT DISTINCT p.id, p.title 
      FROM product p
      INNER JOIN product_category_product pcp ON p.id = pcp.product_id
      WHERE pcp.product_category_id = ANY($1)
      AND p.deleted_at IS NULL
    `, [mokuomoCategoryIds]);
    
    const mokuomoProductIds = mokuomoProductsResult.rows.map(row => row.id);
    console.log(`📦 找到 ${mokuomoProductIds.length} 个 Mokuomo 产品:`, 
      mokuomoProductsResult.rows.slice(0, 5).map(r => r.title));

    // 3. 获取要删除的产品（不在 Mokuomo 产品列表中的）
    let productsToDelete = [];
    if (mokuomoProductIds.length > 0) {
      const productsToDeleteResult = await client.query(`
        SELECT id, title 
        FROM product 
        WHERE id != ALL($1)
        AND deleted_at IS NULL
        AND title NOT LIKE '%Medusa%'
      `, [mokuomoProductIds]);
      
      productsToDelete = productsToDeleteResult.rows;
    } else {
      const productsToDeleteResult = await client.query(`
        SELECT id, title 
        FROM product 
        WHERE deleted_at IS NULL
        AND title NOT LIKE '%Medusa%'
      `);
      
      productsToDelete = productsToDeleteResult.rows;
    }
    
    console.log(`🗑️  将删除 ${productsToDelete.length} 个非 Mokuomo 产品`);

    // 4. 获取要删除的分类（不在 Mokuomo 分类列表中的）
    const categoriesToDeleteResult = await client.query(`
      SELECT id, name, handle 
      FROM product_category 
      WHERE handle != ALL($1)
      AND deleted_at IS NULL
    `, [mokuomoCategories]);
    
    const categoriesToDelete = categoriesToDeleteResult.rows;
    console.log(`🗑️  将删除 ${categoriesToDelete.length} 个非 Mokuomo 分类`);

    // 5. 开始删除操作
    console.log('\n🧹 开始清理操作...');

    // 删除产品（软删除）
    let deletedProductsCount = 0;
    for (const product of productsToDelete) {
      try {
        await client.query(`
          UPDATE product 
          SET deleted_at = NOW(), updated_at = NOW()
          WHERE id = $1
        `, [product.id]);
        
        console.log(`🗑️  删除产品: ${product.title}`);
        deletedProductsCount++;
      } catch (error) {
        console.error(`❌ 删除产品失败 ${product.title}:`, error.message);
      }
    }

    // 删除分类（软删除）
    let deletedCategoriesCount = 0;
    for (const category of categoriesToDelete) {
      try {
        await client.query(`
          UPDATE product_category 
          SET deleted_at = NOW(), updated_at = NOW()
          WHERE id = $1
        `, [category.id]);
        
        console.log(`🗑️  删除分类: ${category.name} (${category.handle})`);
        deletedCategoriesCount++;
      } catch (error) {
        console.error(`❌ 删除分类失败 ${category.name}:`, error.message);
      }
    }

    // 6. 清理孤立的关联记录
    console.log('\n🧹 清理孤立的关联记录...');
    
    // 清理产品-分类关联
    const deletedCategoryProductLinks = await client.query(`
      DELETE FROM product_category_product 
      WHERE product_category_id IN (
        SELECT id FROM product_category WHERE deleted_at IS NOT NULL
      ) OR product_id IN (
        SELECT id FROM product WHERE deleted_at IS NOT NULL
      )
    `);
    
    // 清理产品-销售渠道关联
    const deletedSalesChannelLinks = await client.query(`
      DELETE FROM product_sales_channel 
      WHERE product_id IN (
        SELECT id FROM product WHERE deleted_at IS NOT NULL
      )
    `);

    // 清理产品图片关联
    const deletedImageLinks = await client.query(`
      DELETE FROM product_images 
      WHERE product_id IN (
        SELECT id FROM product WHERE deleted_at IS NOT NULL
      )
    `);

    console.log('\n📊 清理完成统计:');
    console.log(`🗑️  删除产品: ${deletedProductsCount} 个`);
    console.log(`🗑️  删除分类: ${deletedCategoriesCount} 个`);
    console.log(`🧹 清理产品-分类关联: ${deletedCategoryProductLinks.rowCount} 条`);
    console.log(`🧹 清理销售渠道关联: ${deletedSalesChannelLinks.rowCount} 条`);
    console.log(`🧹 清理图片关联: ${deletedImageLinks.rowCount} 条`);

    // 7. 显示保留的数据统计
    console.log('\n✅ 保留的数据统计:');
    console.log(`📁 Mokuomo 分类: ${mokuomoCategoryIds.length} 个`);
    console.log(`📦 Mokuomo 产品: ${mokuomoProductIds.length} 个`);

    // 显示每个分类的产品数量
    console.log('\n📁 各 Mokuomo 分类产品数量:');
    for (const categoryRow of mokuomoCategoriesResult.rows) {
      const countResult = await client.query(`
        SELECT COUNT(*) as count 
        FROM product_category_product pcp
        INNER JOIN product p ON pcp.product_id = p.id
        WHERE pcp.product_category_id = $1 AND p.deleted_at IS NULL
      `, [categoryRow.id]);
      
      const count = countResult.rows[0].count;
      console.log(`  ${categoryRow.name} (${categoryRow.handle}): ${count} 个产品`);
    }

    console.log('\n🎉 数据库清理完成！现在只保留 Mokuomo 的分类和产品。');
    console.log('💡 建议重新同步产品到 Sanity: node sync-products-to-sanity.js');

  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanupDatabase().catch(console.error);
}

module.exports = { cleanupDatabase };
