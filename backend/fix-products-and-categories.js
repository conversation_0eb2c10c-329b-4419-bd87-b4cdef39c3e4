const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

// Mokuomo 分类映射
const mokuomoCategories = [
  { name: 'Combos', handle: 'combos', description: 'Wooden sculpture combo sets and collections' },
  { name: 'Best Sellers', handle: 'best-sellers', description: 'Most popular wooden sculptures' },
  { name: 'Wildlife', handle: 'wildlife', description: 'Wild animal wooden sculptures' },
  { name: 'Bears', handle: 'bears', description: 'Carved wooden bear sculptures' },
  { name: 'Cats', handle: 'cats', description: 'Carved wooden cat sculptures' },
  { name: 'Dogs', handle: 'dogs', description: 'Carved wooden dog sculptures' },
  { name: 'Fun Finds', handle: 'fun-finds', description: 'Unique and playful wooden sculptures' },
  { name: 'Birds', handle: 'birds', description: 'Carved wooden bird sculptures' }
];

// 产品分类映射（基于产品标题关键词）
const productCategoryMapping = {
  'bear': 'bears',
  'cat': 'cats', 
  'dog': 'dogs',
  'bird': 'birds',
  'fox': 'wildlife',
  'whale': 'wildlife',
  'panda': 'wildlife',
  'giraffe': 'wildlife',
  'squirrel': 'wildlife',
  'penguin': 'wildlife',
  'capybara': 'wildlife',
  'combo': 'combos',
  'series': 'combos',
  'family': 'combos',
  'set': 'combos'
};

async function getExistingImages(client) {
  try {
    const result = await client.query(`
      SELECT DISTINCT i.url
      FROM image i
      INNER JOIN product_images pi ON i.id = pi.image_id
      WHERE i.url LIKE 'http://localhost:9000%'
      AND i.deleted_at IS NULL
      ORDER BY i.url
    `);

    const images = result.rows.map(row => row.url);
    console.log(`📸 找到 ${images.length} 个现有图片`);
    return images;
  } catch (error) {
    console.error('获取现有图片失败:', error);
    return [];
  }
}

function getRandomImages(imagePool, count = 3) {
  const shuffled = [...imagePool].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.min(count, imagePool.length));
}

function categorizeProduct(title) {
  const titleLower = title.toLowerCase();
  
  for (const [keyword, category] of Object.entries(productCategoryMapping)) {
    if (titleLower.includes(keyword)) {
      return category;
    }
  }
  
  // 默认分类
  return 'fun-finds';
}

async function createCategories(client) {
  console.log('📁 创建产品分类...');
  const createdCategories = {};
  
  for (const category of mokuomoCategories) {
    try {
      const categoryId = `pcol_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 先检查是否已存在
      const existingResult = await client.query(
        'SELECT id FROM product_collection WHERE handle = $1',
        [category.handle]
      );

      if (existingResult.rows.length > 0) {
        createdCategories[category.handle] = existingResult.rows[0].id;
        console.log(`✅ 分类已存在: ${category.name} (${category.handle})`);
        continue;
      }

      const insertQuery = `
        INSERT INTO product_collection (id, title, handle, metadata, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        RETURNING id
      `;
      
      const metadata = JSON.stringify({ 
        description: category.description,
        source: 'mokuomo'
      });
      
      const result = await client.query(insertQuery, [
        categoryId,
        category.name,
        category.handle,
        metadata
      ]);

      createdCategories[category.handle] = result.rows[0].id;
      
      console.log(`✅ 创建/更新分类: ${category.name} (${category.handle})`);
    } catch (error) {
      console.error(`❌ 创建分类失败 ${category.name}:`, error.message);
    }
  }
  
  return createdCategories;
}

async function addProductImages(client, productId, imageUrls) {
  try {
    // 先删除现有的产品图片关联
    await client.query('DELETE FROM product_images WHERE product_id = $1', [productId]);

    // 为每个图片URL创建或获取image记录，然后关联到产品
    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i];

      // 检查图片是否已存在
      let imageResult = await client.query(
        'SELECT id FROM image WHERE url = $1 AND deleted_at IS NULL',
        [imageUrl]
      );

      let imageId;
      if (imageResult.rows.length > 0) {
        imageId = imageResult.rows[0].id;
      } else {
        // 创建新的图片记录
        imageId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await client.query(`
          INSERT INTO image (id, url, metadata, created_at, updated_at)
          VALUES ($1, $2, $3, NOW(), NOW())
        `, [imageId, imageUrl, JSON.stringify({ rank: i })]);
      }

      // 关联图片到产品
      await client.query(`
        INSERT INTO product_images (product_id, image_id)
        VALUES ($1, $2)
        ON CONFLICT DO NOTHING
      `, [productId, imageId]);
    }

    // 更新产品的 thumbnail
    await client.query(`
      UPDATE product SET thumbnail = $1, updated_at = NOW()
      WHERE id = $2
    `, [imageUrls[0], productId]);

  } catch (error) {
    console.error(`添加产品图片失败 ${productId}:`, error.message);
  }
}

async function linkProductToCategory(client, productId, categoryId) {
  try {
    // 检查是否已经关联
    const existingResult = await client.query(`
      SELECT id FROM product_category_product 
      WHERE product_id = $1 AND product_category_id = $2
    `, [productId, categoryId]);
    
    if (existingResult.rows.length === 0) {
      const linkId = `pcp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await client.query(`
        INSERT INTO product_category_product (id, product_id, product_category_id, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
      `, [linkId, productId, categoryId]);
    }
  } catch (error) {
    console.error(`关联产品到分类失败:`, error.message);
  }
}

async function fixProductsAndCategories() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 开始修复产品图片和分类...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 获取现有图片池
    const imagePool = await getExistingImages(client);
    if (imagePool.length === 0) {
      console.log('❌ 没有找到现有图片，无法为新产品添加图片');
      return;
    }

    // 2. 创建分类
    const categories = await createCategories(client);
    console.log(`📁 创建了 ${Object.keys(categories).length} 个分类`);

    // 3. 获取没有图片的新产品
    const productsResult = await client.query(`
      SELECT p.id, p.title, p.handle
      FROM product p
      LEFT JOIN product_images pi ON p.id = pi.product_id
      WHERE pi.product_id IS NULL
      AND (p.title LIKE '%Carved%' OR p.title LIKE '%Wooden%')
      ORDER BY p.created_at DESC
    `);
    
    const productsWithoutImages = productsResult.rows;
    console.log(`📦 找到 ${productsWithoutImages.length} 个需要添加图片的产品`);

    let successCount = 0;
    let failCount = 0;

    // 4. 为每个产品添加图片和分类
    for (const product of productsWithoutImages) {
      try {
        // 添加随机图片
        const randomImages = getRandomImages(imagePool, 3);
        await addProductImages(client, product.id, randomImages);
        
        // 确定产品分类
        const categoryHandle = categorizeProduct(product.title);
        const categoryId = categories[categoryHandle];
        
        if (categoryId) {
          await linkProductToCategory(client, product.id, categoryId);
          console.log(`✅ 处理产品: ${product.title} -> ${categoryHandle} (${randomImages.length} 张图片)`);
        } else {
          console.log(`⚠️  未找到分类 ${categoryHandle} 的ID: ${product.title}`);
        }
        
        successCount++;
        
        // 添加延迟避免数据库压力
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`❌ 处理产品失败 ${product.title}:`, error.message);
        failCount++;
      }
    }

    console.log('\n📊 修复完成统计:');
    console.log(`✅ 成功: ${successCount} 个产品`);
    console.log(`❌ 失败: ${failCount} 个产品`);
    console.log(`📦 总计: ${productsWithoutImages.length} 个产品`);
    console.log(`📁 分类: ${Object.keys(categories).length} 个`);
    
    if (successCount > 0) {
      console.log('\n🎉 产品图片和分类修复完成！');
      console.log('💡 建议重新同步产品到 Sanity: node sync-products-to-sanity.js');
    }

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixProductsAndCategories().catch(console.error);
}

module.exports = { fixProductsAndCategories };
