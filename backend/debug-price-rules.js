const { Client } = require('pg');

const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function debugPriceRules() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔍 调试价格规则关联...\n');

    const variantId = 'variant_1755366597671_iwbnqn0ct';
    const regionId = 'reg_mecot5lgy79vtq0q8';

    // 1. 检查变体的价格集合
    console.log('1️⃣ 检查变体的价格集合...');
    const variantPriceSetResult = await client.query(`
      SELECT 
        pv.id as variant_id,
        pv.title as variant_title,
        pvps.price_set_id,
        ps.id as price_set_id_check
      FROM product_variant pv
      INNER JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id
      LEFT JOIN price_set ps ON pvps.price_set_id = ps.id
      WHERE pv.id = $1
      AND pv.deleted_at IS NULL
      AND pvps.deleted_at IS NULL
    `, [variantId]);

    if (variantPriceSetResult.rows.length === 0) {
      console.log('❌ 变体没有关联的价格集合');
      return;
    }

    const priceSetId = variantPriceSetResult.rows[0].price_set_id;
    console.log(`✅ 变体关联的价格集合: ${priceSetId}`);

    // 2. 检查价格集合中的价格
    console.log('\n2️⃣ 检查价格集合中的价格...');
    const pricesResult = await client.query(`
      SELECT 
        p.id as price_id,
        p.amount,
        p.currency_code,
        p.price_set_id
      FROM price p
      WHERE p.price_set_id = $1
      AND p.deleted_at IS NULL
    `, [priceSetId]);

    if (pricesResult.rows.length === 0) {
      console.log('❌ 价格集合中没有价格');
      return;
    }

    console.log(`✅ 找到 ${pricesResult.rows.length} 个价格:`);
    pricesResult.rows.forEach(price => {
      console.log(`   - 价格 ${price.price_id}: $${(price.amount / 100).toFixed(2)} ${price.currency_code}`);
    });

    // 3. 检查每个价格的规则
    console.log('\n3️⃣ 检查价格规则...');
    for (const price of pricesResult.rows) {
      const rulesResult = await client.query(`
        SELECT 
          pr.id as rule_id,
          pr.attribute,
          pr.value,
          pr.priority
        FROM price_rule pr
        WHERE pr.price_id = $1
        AND pr.deleted_at IS NULL
        ORDER BY pr.priority DESC
      `, [price.price_id]);

      console.log(`   价格 ${price.price_id} ($${(price.amount / 100).toFixed(2)}):`);
      if (rulesResult.rows.length === 0) {
        console.log('     ❌ 没有规则');
      } else {
        rulesResult.rows.forEach(rule => {
          console.log(`     - 规则 ${rule.rule_id}: ${rule.attribute}=${rule.value} (优先级: ${rule.priority})`);
        });
      }
    }

    // 4. 检查是否有针对 US 区域的规则
    console.log('\n4️⃣ 检查 US 区域规则...');
    const usRulesResult = await client.query(`
      SELECT 
        p.id as price_id,
        p.amount,
        p.currency_code,
        pr.id as rule_id,
        pr.attribute,
        pr.value,
        pr.priority
      FROM price p
      INNER JOIN price_rule pr ON p.id = pr.price_id
      WHERE p.price_set_id = $1
      AND pr.attribute = 'region_id'
      AND pr.value = $2
      AND p.deleted_at IS NULL
      AND pr.deleted_at IS NULL
      ORDER BY pr.priority DESC
    `, [priceSetId, regionId]);

    if (usRulesResult.rows.length === 0) {
      console.log('❌ 没有找到 US 区域的价格规则');
    } else {
      console.log(`✅ 找到 ${usRulesResult.rows.length} 个 US 区域规则:`);
      usRulesResult.rows.forEach(rule => {
        console.log(`   - 价格 ${rule.price_id}: $${(rule.amount / 100).toFixed(2)} ${rule.currency_code}`);
        console.log(`     规则 ${rule.rule_id}: ${rule.attribute}=${rule.value} (优先级: ${rule.priority})`);
      });
    }

    // 5. 模拟 Medusa 的价格查询
    console.log('\n5️⃣ 模拟 Medusa 价格查询...');
    const medusaQueryResult = await client.query(`
      SELECT 
        p.id as price_id,
        p.amount,
        p.currency_code,
        pr.id as rule_id,
        pr.attribute,
        pr.value,
        pr.priority,
        COUNT(*) OVER (PARTITION BY p.id) as rule_count
      FROM price p
      LEFT JOIN price_rule pr ON p.id = pr.price_id AND pr.deleted_at IS NULL
      WHERE p.price_set_id = $1
      AND p.deleted_at IS NULL
      AND (
        pr.id IS NULL OR 
        (pr.attribute = 'region_id' AND pr.value = $2)
      )
      ORDER BY p.id, pr.priority DESC
    `, [priceSetId, regionId]);

    console.log('Medusa 查询结果:');
    if (medusaQueryResult.rows.length === 0) {
      console.log('❌ 没有匹配的价格');
    } else {
      medusaQueryResult.rows.forEach(row => {
        console.log(`   - 价格 ${row.price_id}: $${(row.amount / 100).toFixed(2)} ${row.currency_code}`);
        if (row.rule_id) {
          console.log(`     规则: ${row.attribute}=${row.value} (优先级: ${row.priority})`);
        } else {
          console.log('     无规则 (默认价格)');
        }
      });
    }

    // 6. 建议修复方案
    console.log('\n💡 修复建议:');
    console.log('='.repeat(50));
    
    if (usRulesResult.rows.length > 0) {
      console.log('✅ US 区域规则存在，问题可能在于:');
      console.log('   1. Medusa 价格计算引擎缓存问题');
      console.log('   2. 价格规则优先级设置');
      console.log('   3. 需要重启 Medusa 服务');
    } else {
      console.log('❌ 缺少 US 区域规则，需要创建');
    }

  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await client.end();
  }
}

if (require.main === module) {
  debugPriceRules().catch(console.error);
}

module.exports = { debugPriceRules };
