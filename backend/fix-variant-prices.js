const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

async function fixVariantPrices() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔧 开始修复变体价格问题...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 检查错误中提到的变体
    const problemVariantId = 'variant_1755366640268_ae3h859og';
    console.log(`\n🔍 检查问题变体: ${problemVariantId}`);
    
    const variantCheckResult = await client.query(`
      SELECT pv.id, pv.title, p.title as product_title, p.id as product_id
      FROM product_variant pv 
      INNER JOIN product p ON pv.product_id = p.id 
      WHERE pv.id = $1
    `, [problemVariantId]);
    
    if (variantCheckResult.rows.length === 0) {
      console.log(`❌ 变体 ${problemVariantId} 不存在`);
    } else {
      const variant = variantCheckResult.rows[0];
      console.log(`✅ 找到变体: ${variant.title} (产品: ${variant.product_title})`);
      
      // 检查是否有价格
      const priceCheckResult = await client.query(`
        SELECT pvps.price_set_id, p.amount, p.currency_code
        FROM product_variant_price_set pvps 
        INNER JOIN price p ON pvps.price_set_id = p.price_set_id 
        WHERE pvps.variant_id = $1 AND pvps.deleted_at IS NULL AND p.deleted_at IS NULL
      `, [problemVariantId]);
      
      if (priceCheckResult.rows.length === 0) {
        console.log(`❌ 变体 ${problemVariantId} 没有价格数据，正在创建...`);
        
        // 为这个变体创建价格
        const price = Math.round((Math.random() * 30 + 20) * 100); // $20-$50
        const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 创建 price_set
        await client.query(`
          INSERT INTO price_set (id, created_at, updated_at)
          VALUES ($1, NOW(), NOW())
        `, [priceSetId]);
        
        // 创建 price
        const priceId = `price_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await client.query(`
          INSERT INTO price (id, price_set_id, currency_code, amount, raw_amount, rules_count, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        `, [priceId, priceSetId, 'usd', price, JSON.stringify({value: price}), 0]);
        
        // 关联 variant 到 price_set
        const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await client.query(`
          INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at)
          VALUES ($1, $2, $3, NOW(), NOW())
        `, [linkId, problemVariantId, priceSetId]);
        
        console.log(`✅ 为变体 ${problemVariantId} 创建价格: $${(price / 100).toFixed(2)}`);
      } else {
        console.log(`✅ 变体 ${problemVariantId} 已有价格: $${(priceCheckResult.rows[0].amount / 100).toFixed(2)}`);
      }
    }

    // 2. 检查所有 Mokuomo 产品的变体是否都有价格
    console.log('\n📋 检查所有 Mokuomo 产品变体的价格...');
    const allVariantsResult = await client.query(`
      SELECT 
        pv.id as variant_id,
        pv.title as variant_title,
        p.title as product_title,
        pvps.price_set_id,
        pr.amount
      FROM product_variant pv 
      INNER JOIN product p ON pv.product_id = p.id 
      LEFT JOIN product_variant_price_set pvps ON pv.id = pvps.variant_id AND pvps.deleted_at IS NULL
      LEFT JOIN price pr ON pvps.price_set_id = pr.price_set_id AND pr.deleted_at IS NULL
      WHERE p.deleted_at IS NULL 
      AND pv.deleted_at IS NULL
      AND (p.title LIKE '%Carved%' OR p.title LIKE '%Wooden%')
      ORDER BY p.title, pv.title
    `);
    
    console.log(`找到 ${allVariantsResult.rows.length} 个 Mokuomo 产品变体`);
    
    let missingPriceCount = 0;
    let fixedCount = 0;
    
    for (const variant of allVariantsResult.rows) {
      if (!variant.price_set_id || !variant.amount) {
        console.log(`❌ 变体缺少价格: ${variant.variant_title} (${variant.product_title})`);
        missingPriceCount++;
        
        // 创建价格
        const price = Math.round((Math.random() * 30 + 20) * 100); // $20-$50
        const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
          // 创建 price_set
          await client.query(`
            INSERT INTO price_set (id, created_at, updated_at)
            VALUES ($1, NOW(), NOW())
          `, [priceSetId]);
          
          // 创建 price
          const priceId = `price_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          await client.query(`
            INSERT INTO price (id, price_set_id, currency_code, amount, raw_amount, rules_count, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
          `, [priceId, priceSetId, 'usd', price, JSON.stringify({value: price}), 0]);
          
          // 关联 variant 到 price_set
          const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          await client.query(`
            INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at)
            VALUES ($1, $2, $3, NOW(), NOW())
          `, [linkId, variant.variant_id, priceSetId]);
          
          console.log(`  ✅ 修复成功: $${(price / 100).toFixed(2)}`);
          fixedCount++;
          
          // 添加延迟避免 ID 冲突
          await new Promise(resolve => setTimeout(resolve, 10));
          
        } catch (error) {
          console.error(`  ❌ 修复失败: ${error.message}`);
        }
      } else {
        console.log(`✅ 变体有价格: ${variant.variant_title} - $${(variant.amount / 100).toFixed(2)}`);
      }
    }

    console.log('\n📊 修复统计:');
    console.log(`总变体数: ${allVariantsResult.rows.length}`);
    console.log(`缺少价格: ${missingPriceCount}`);
    console.log(`修复成功: ${fixedCount}`);
    
    if (fixedCount > 0) {
      console.log('\n🎉 价格修复完成！');
      console.log('💡 建议重启 Medusa 服务以应用更改');
    } else {
      console.log('\n✅ 所有变体都有价格数据');
    }

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixVariantPrices().catch(console.error);
}

module.exports = { fixVariantPrices };
