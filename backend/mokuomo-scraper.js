const fetch = require('node-fetch').default || require('node-fetch');
const cheerio = require('cheerio');
const { Client } = require('pg');

// 数据库连接配置 - 使用 Medusa 的数据库 URL
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

// Mokuomo 分类映射
const categories = [
  { name: 'Combos', handle: 'combos', url: '/collections/combo' },
  { name: 'Best Sellers', handle: 'best-sellers', url: '/collections/best-seller' },
  { name: 'Wildlife', handle: 'wildlife', url: '/collections/wildlife' },
  { name: 'Bears', handle: 'bears', url: '/collections/bears' },
  { name: 'Cats', handle: 'cats', url: '/collections/cats' },
  { name: 'Dogs', handle: 'dogs', url: '/collections/dogs' },
  { name: 'Fun Finds', handle: 'fun-finds', url: '/collections/fun-finds' },
  { name: 'Birds', handle: 'birds', url: '/collections/birds' }
];

// 获取现有的 collection IDs
async function getExistingCollections(client) {
  const result = await client.query('SELECT id, handle FROM product_collection');
  return result.rows;
}

// 随机分配产品到现有 collection
function getRandomCollectionId(collections) {
  const randomIndex = Math.floor(Math.random() * collections.length);
  return collections[randomIndex].id;
}

// 创建分类
async function createCategory(client, category) {
  try {
    const categoryId = `pcol_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const insertQuery = `
      INSERT INTO product_collection (id, title, handle, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
      RETURNING id
    `;
    
    const result = await client.query(insertQuery, [categoryId, category.name, category.handle]);
    console.log(`✅ Created category: ${category.name} (${categoryId})`);
    return result.rows[0].id;
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      console.log(`⚠️  Category ${category.name} already exists`);
      const result = await client.query('SELECT id FROM product_collection WHERE handle = $1', [category.handle]);
      return result.rows[0]?.id;
    }
    throw error;
  }
}

// 抓取产品列表
async function scrapeProductList(categoryUrl) {
  try {
    console.log(`🔍 Scraping products from: ${categoryUrl}`);
    const response = await fetch(`https://www.mokuomo.com${categoryUrl}`);
    const html = await response.text();
    const $ = cheerio.load(html);
    
    const products = [];
    
    // 查找产品链接
    $('a[href*="/products/"]').each((index, element) => {
      const href = $(element).attr('href');
      if (href && href.includes('/products/') && !href.includes('#')) {
        const productUrl = href.startsWith('http') ? href : `https://www.mokuomo.com${href}`;
        if (!products.some(p => p.url === productUrl)) {
          products.push({ url: productUrl });
        }
      }
    });
    
    console.log(`📦 Found ${products.length} products in category`);
    return products;
  } catch (error) {
    console.error(`❌ Error scraping product list from ${categoryUrl}:`, error.message);
    return [];
  }
}

// 抓取产品详情
async function scrapeProductDetails(productUrl) {
  try {
    console.log(`🔍 Scraping product details: ${productUrl}`);
    const response = await fetch(productUrl);
    const html = await response.text();
    const $ = cheerio.load(html);
    
    // 提取产品信息
    const title = $('h1').first().text().trim();
    const handle = productUrl.split('/products/')[1]?.split('?')[0] || '';
    
    // 提取价格 (HK$ 转换为 USD)
    const priceText = $('.price, [class*="price"]').first().text();
    const priceMatch = priceText.match(/HK\$(\d+)/);
    const hkdPrice = priceMatch ? parseInt(priceMatch[1]) : 88;
    const usdPrice = Math.round(hkdPrice * 0.13 * 100); // HKD to USD, convert to cents
    
    // 提取描述
    const description = $('[class*="description"], .product-description, .rte').first().text().trim() || 
                      'Handcrafted wooden sculpture with natural wood material. Cute, unique and adorable decoration piece.';
    
    // 提取图片
    const images = [];
    $('img[src*="mokuomo.com"]').each((index, img) => {
      const src = $(img).attr('src');
      if (src && src.includes('mokuomo.com') && !src.includes('logo') && index < 5) {
        // 确保使用高质量图片
        const imageUrl = src.replace('_1024x1024', '').replace('_300x', '').split('?')[0];
        if (!images.includes(imageUrl)) {
          images.push(imageUrl);
        }
      }
    });
    
    // 提取规格信息
    const specs = {};
    $('li, p').each((index, element) => {
      const text = $(element).text();
      if (text.includes('Size:') || text.includes('Approx.Size:')) {
        specs.dimensions = text.replace(/.*Size:\s*/, '').trim();
      }
      if (text.includes('Weight:')) {
        specs.weight = text.replace(/.*Weight:\s*/, '').trim();
      }
      if (text.includes('Material:')) {
        specs.material = text.replace(/.*Material:\s*/, '').trim();
      }
    });
    
    const product = {
      title: title || 'Wooden Sculpture',
      handle: handle || `wooden-sculpture-${Date.now()}`,
      description: description.substring(0, 500), // Limit description length
      price: usdPrice,
      images: images.slice(0, 3), // Limit to 3 images
      material: specs.material || 'Natural basswood',
      weight: specs.weight || '~40g',
      dimensions: specs.dimensions || '~4x3x4 cm',
      origin_country: 'HK'
    };
    
    console.log(`✅ Scraped product: ${product.title}`);
    return product;
  } catch (error) {
    console.error(`❌ Error scraping product ${productUrl}:`, error.message);
    return null;
  }
}

// 创建产品
async function createProduct(client, product, collectionId) {
  try {
    const productId = `prod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 插入产品
    const productQuery = `
      INSERT INTO product (
        id, title, handle, description, material, weight, length, height, width,
        origin_country, status, is_giftcard, discountable, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW(), NOW())
      RETURNING id
    `;
    
    // 解析尺寸
    const dimensions = product.dimensions.match(/(\d+).*?(\d+).*?(\d+)/);
    const length = dimensions ? parseInt(dimensions[1]) : 40;
    const height = dimensions ? parseInt(dimensions[2]) : 30;
    const width = dimensions ? parseInt(dimensions[3]) : 40;
    
    await client.query(productQuery, [
      productId, product.title, product.handle, product.description,
      product.material, product.weight, length, height, width, product.origin_country,
      'published', false, true // status, is_giftcard, discountable
    ]);
    
    // 创建产品变体
    const variantId = `variant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const variantQuery = `
      INSERT INTO product_variant (
        id, product_id, title, sku, manage_inventory, allow_backorder
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `;

    await client.query(variantQuery, [
      variantId, productId, 'Default', product.handle.toUpperCase(), true, false
    ]);
    
    // 注意：价格需要通过 Medusa Admin API 或者更复杂的价格系统设置
    // 这里暂时跳过价格设置，专注于产品创建
    
    // 关联到 collection (暂时跳过，因为表结构可能不同)
    // 可以稍后通过 Medusa Admin API 设置产品分类
    
    console.log(`✅ Created product: ${product.title} (${productId})`);
    return productId;
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      console.log(`⚠️  Product ${product.title} already exists`);
      return null;
    }
    throw error;
  }
}

// 主函数
async function main() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 Starting Mokuomo data import...');
    await client.connect();
    console.log('✅ Connected to database');
    
    // 获取现有的 collections
    const existingCollections = await getExistingCollections(client);
    console.log(`📂 Found ${existingCollections.length} existing collections`);
    
    if (existingCollections.length === 0) {
      console.log('❌ No existing collections found. Please create some collections first.');
      return;
    }
    
    let totalProducts = 0;
    let successfulProducts = 0;
    
    // 处理每个分类
    for (const category of categories.slice(0, 3)) { // 限制前3个分类进行测试
      console.log(`\n📁 Processing category: ${category.name}`);
      
      // 抓取产品列表
      const productUrls = await scrapeProductList(category.url);
      
      // 限制每个分类最多10个产品
      const limitedProducts = productUrls.slice(0, 10);
      
      for (const productInfo of limitedProducts) {
        totalProducts++;
        
        // 抓取产品详情
        const product = await scrapeProductDetails(productInfo.url);
        
        if (product) {
          // 随机分配到现有 collection
          const randomCollectionId = getRandomCollectionId(existingCollections);
          
          // 创建产品
          const productId = await createProduct(client, product, randomCollectionId);
          
          if (productId) {
            successfulProducts++;
          }
        }
        
        // 添加延迟避免被封
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    console.log('\n📊 Import completed:');
    console.log(`✅ Successfully imported: ${successfulProducts} products`);
    console.log(`❌ Failed: ${totalProducts - successfulProducts} products`);
    console.log(`📦 Total processed: ${totalProducts} products`);
    
  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, scrapeProductDetails, createProduct };
