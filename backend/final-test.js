async function finalTest() {
  const fetch = (await import('node-fetch')).default;
  
  console.log('🎯 最终测试 - 验证价格修复是否成功\n');

  const baseUrl = 'http://localhost:9000';
  const publishableKey = 'pk_f3bf2b6ad7a6898050f237e15b93dea6dac071ae49c29bfbe5e9b1dad90ee2b1';
  const regionId = 'reg_mecot5lgy79vtq0q8';
  const productHandle = 'tomo-carved-wooden-brown-bear';

  try {
    // 1. 测试产品价格获取
    console.log('1️⃣ 测试产品价格获取...');
    const productResponse = await fetch(
      `${baseUrl}/store/products?fields=*variants.calculated_price,*variants.prices&handle=${productHandle}&region_id=${regionId}`,
      {
        headers: {
          'x-publishable-api-key': publishableKey,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!productResponse.ok) {
      throw new Error(`产品请求失败: ${productResponse.status}`);
    }

    const productData = await productResponse.json();
    const product = productData.products[0];
    const variant = product.variants[0];

    console.log(`✅ 产品: ${product.title}`);
    console.log(`   变体: ${variant.title}`);
    console.log(`   calculated_price: ${variant.calculated_price ? 'exists' : 'null'}`);
    console.log(`   prices: ${variant.prices ? 'exists' : 'undefined'}`);
    
    if (variant.prices && variant.prices.length > 0) {
      const price = variant.prices[0];
      console.log(`   原始价格: $${(price.amount / 100).toFixed(2)} ${price.currency_code}`);
    }

    // 2. 测试购物车添加
    console.log('\n2️⃣ 测试购物车添加...');
    
    // 创建购物车
    const cartResponse = await fetch(`${baseUrl}/store/carts`, {
      method: 'POST',
      headers: {
        'x-publishable-api-key': publishableKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        region_id: regionId,
      }),
    });

    if (!cartResponse.ok) {
      throw new Error(`创建购物车失败: ${cartResponse.status}`);
    }

    const cartData = await cartResponse.json();
    const cartId = cartData.cart.id;
    console.log(`✅ 购物车创建成功: ${cartId}`);

    // 添加商品到购物车
    const addToCartResponse = await fetch(`${baseUrl}/store/carts/${cartId}/line-items`, {
      method: 'POST',
      headers: {
        'x-publishable-api-key': publishableKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        variant_id: variant.id,
        quantity: 1,
      }),
    });

    if (addToCartResponse.ok) {
      const updatedCart = await addToCartResponse.json();
      console.log('✅ 添加到购物车成功');
      
      if (updatedCart.cart.items?.length > 0) {
        const item = updatedCart.cart.items[0];
        console.log(`   商品价格: $${(item.unit_price / 100).toFixed(2)}`);
        console.log(`   总价: $${(item.total / 100).toFixed(2)}`);
        console.log(`   数量: ${item.quantity}`);
      }
    } else {
      const errorText = await addToCartResponse.text();
      console.log(`❌ 添加到购物车失败: ${addToCartResponse.status}`);
      console.log(`   错误信息: ${errorText}`);
    }

    // 3. 验证价格规则
    console.log('\n3️⃣ 验证价格规则...');
    const { Client } = require('pg');
    const client = new Client({
      connectionString: 'postgresql://localhost:5432/medusa_dev'
    });
    
    await client.connect();
    
    const priceRulesResult = await client.query(`
      SELECT COUNT(*) as count
      FROM price_rule pr
      INNER JOIN price p ON pr.price_id = p.id
      INNER JOIN product_variant_price_set pvps ON p.price_set_id = pvps.price_set_id
      INNER JOIN product_variant pv ON pvps.variant_id = pv.id
      INNER JOIN product prod ON pv.product_id = prod.id
      WHERE pr.attribute = 'region_id' 
      AND pr.value = $1 
      AND pr.deleted_at IS NULL
      AND (prod.title LIKE '%Carved%' OR prod.title LIKE '%Wooden%')
    `, [regionId]);
    
    const ruleCount = priceRulesResult.rows[0].count;
    console.log(`✅ US 区域价格规则数量: ${ruleCount}`);
    
    await client.end();

    // 4. 总结
    console.log('\n📊 测试总结:');
    console.log('='.repeat(50));
    
    const hasOriginalPrice = variant.prices && variant.prices.length > 0;
    const canAddToCart = addToCartResponse.ok;
    const hasEnoughRules = parseInt(ruleCount) > 0;
    
    console.log(`✅ 原始价格数据: ${hasOriginalPrice ? '存在' : '❌ 缺失'}`);
    console.log(`✅ 购物车功能: ${canAddToCart ? '正常' : '❌ 失败'}`);
    console.log(`✅ 价格规则: ${hasEnoughRules ? '已修复' : '❌ 缺失'}`);
    
    if (hasOriginalPrice && canAddToCart && hasEnoughRules) {
      console.log('\n🎉 所有测试通过！价格问题已完全修复！');
      console.log('💡 前端现在应该能正确显示价格并支持购物车功能');
    } else {
      console.log('\n⚠️  部分测试失败，需要进一步调试');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

if (require.main === module) {
  finalTest().catch(console.error);
}

module.exports = { finalTest };
