const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

// 产品分类映射（基于产品标题关键词）
const productCategoryMapping = {
  'bear': 'bears',
  'cat': 'cats', 
  'dog': 'dogs',
  'bird': 'birds',
  'fox': 'wildlife',
  'whale': 'wildlife',
  'panda': 'wildlife',
  'giraffe': 'wildlife',
  'squirrel': 'wildlife',
  'penguin': 'wildlife',
  'capybara': 'wildlife',
  'combo': 'combos',
  'series': 'combos',
  'family': 'combos',
  'set': 'combos',
  'crew': 'fun-finds',
  'friends': 'fun-finds',
  'yoga': 'fun-finds'
};

function categorizeProduct(title) {
  const titleLower = title.toLowerCase();
  
  for (const [keyword, category] of Object.entries(productCategoryMapping)) {
    if (titleLower.includes(keyword)) {
      return category;
    }
  }
  
  // 默认分类
  return 'fun-finds';
}

async function linkProductsToCategories() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 开始将产品绑定到分类...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 1. 获取所有分类
    const categoriesResult = await client.query('SELECT id, handle FROM product_collection');
    const categories = {};
    categoriesResult.rows.forEach(row => {
      categories[row.handle] = row.id;
    });
    
    console.log(`📁 找到 ${Object.keys(categories).length} 个分类:`, Object.keys(categories));

    // 2. 获取所有新导入的产品（Carved 或 Wooden）
    const productsResult = await client.query(`
      SELECT id, title, handle 
      FROM product 
      WHERE (title LIKE '%Carved%' OR title LIKE '%Wooden%')
      AND status = 'published'
      ORDER BY created_at DESC
    `);
    
    const products = productsResult.rows;
    console.log(`📦 找到 ${products.length} 个需要分类的产品`);

    let successCount = 0;
    let failCount = 0;

    // 3. 为每个产品分配分类
    for (const product of products) {
      try {
        // 确定产品分类
        const categoryHandle = categorizeProduct(product.title);
        const categoryId = categories[categoryHandle];
        
        if (!categoryId) {
          console.log(`⚠️  未找到分类 ${categoryHandle}: ${product.title}`);
          failCount++;
          continue;
        }

        // 检查是否已经关联
        const existingResult = await client.query(`
          SELECT collection_id FROM product
          WHERE id = $1
        `, [product.id]);

        if (existingResult.rows[0]?.collection_id === categoryId) {
          console.log(`✅ 已关联: ${product.title} -> ${categoryHandle}`);
          successCount++;
          continue;
        }

        // 更新产品的 collection_id
        await client.query(`
          UPDATE product
          SET collection_id = $1, updated_at = NOW()
          WHERE id = $2
        `, [categoryId, product.id]);
        
        console.log(`✅ 新关联: ${product.title} -> ${categoryHandle}`);
        successCount++;
        
      } catch (error) {
        console.error(`❌ 关联失败 ${product.title}:`, error.message);
        failCount++;
      }
    }

    console.log('\n📊 分类绑定完成统计:');
    console.log(`✅ 成功: ${successCount} 个产品`);
    console.log(`❌ 失败: ${failCount} 个产品`);
    console.log(`📦 总计: ${products.length} 个产品`);
    
    // 4. 显示每个分类的产品数量
    console.log('\n📁 各分类产品数量:');
    for (const [handle, categoryId] of Object.entries(categories)) {
      const countResult = await client.query(`
        SELECT COUNT(*) as count
        FROM product
        WHERE collection_id = $1 AND deleted_at IS NULL
      `, [categoryId]);

      const count = countResult.rows[0].count;
      console.log(`  ${handle}: ${count} 个产品`);
    }
    
    if (successCount > 0) {
      console.log('\n🎉 产品分类绑定完成！');
      console.log('💡 建议重新同步产品到 Sanity: node sync-products-to-sanity.js');
    }

  } catch (error) {
    console.error('❌ 绑定失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  linkProductsToCategories().catch(console.error);
}

module.exports = { linkProductsToCategories };
