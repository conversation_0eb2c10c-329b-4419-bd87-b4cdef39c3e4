const cron = require('node-cron');
const { syncAllProducts } = require('./sync-products-to-sanity');

console.log('🕐 启动产品同步定时任务...');

// 每小时同步一次产品数据
cron.schedule('0 * * * *', async () => {
  console.log('⏰ 开始定时同步产品数据...');
  try {
    await syncAllProducts();
    console.log('✅ 定时同步完成');
  } catch (error) {
    console.error('❌ 定时同步失败:', error);
  }
}, {
  scheduled: true,
  timezone: "Asia/Shanghai"
});

// 每天凌晨 2 点进行完整同步
cron.schedule('0 2 * * *', async () => {
  console.log('🌙 开始每日完整同步...');
  try {
    await syncAllProducts();
    console.log('✅ 每日完整同步完成');
  } catch (error) {
    console.error('❌ 每日完整同步失败:', error);
  }
}, {
  scheduled: true,
  timezone: "Asia/Shanghai"
});

console.log('✅ 定时任务已启动');
console.log('📅 每小时同步: 0 * * * *');
console.log('🌙 每日同步: 0 2 * * *');

// 保持进程运行
process.on('SIGINT', () => {
  console.log('👋 停止定时任务...');
  process.exit(0);
});
