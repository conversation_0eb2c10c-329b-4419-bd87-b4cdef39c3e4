const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  connectionString: 'postgresql://localhost:5432/medusa_dev'
};

// Mock 数据模板
const mockDescriptions = [
  "Handcrafted with Natural Wood\n\nHandcrafted\nCute, unique and adorable\nWonderful gift for kids and loved ones\nAmazing table decoration\nMindful Moments in Solid Wood",
  "Premium Wooden Sculpture\n\nArtisan crafted from sustainable wood\nUnique design with attention to detail\nPerfect for home decoration\nMakes a thoughtful gift\nDurable and long-lasting",
  "Authentic Wood Carving\n\nTraditional craftsmanship\nNatural wood finish\nEco-friendly materials\nDecorative art piece\nConversation starter"
];

const mockIngredients = [
  "Materials: Premium hardwood (Oak/Maple)\nFinish: Natural wood oil\nCraftsmanship: Hand-carved\nOrigin: Artisan workshop",
  "Materials: Sustainable bamboo wood\nFinish: Eco-friendly lacquer\nCraftsmanship: Traditional carving\nOrigin: Handmade with care",
  "Materials: Reclaimed wood\nFinish: Natural beeswax\nCraftsmanship: Precision carved\nOrigin: Artisan collective"
];

const mockShipping = [
  "Free shipping on orders over $50\nStandard delivery: 5-7 business days\nExpress delivery: 2-3 business days\nInternational shipping available\nCarefully packaged for protection",
  "Worldwide shipping available\nStandard: 7-10 business days\nExpress: 3-5 business days\nFree shipping on orders $75+\nSecure packaging guaranteed",
  "Global delivery service\nStandard: 5-8 business days\nPriority: 2-4 business days\nFree shipping threshold: $60\nEco-friendly packaging"
];

// 产品选项模板
const sizeOptions = [
  { name: 'Small', value: 'small' },
  { name: 'Medium', value: 'medium' },
  { name: 'Large', value: 'large' }
];

const colorOptions = [
  { name: 'Natural', value: 'natural' },
  { name: 'Dark Brown', value: 'dark-brown' },
  { name: 'Light Oak', value: 'light-oak' }
];

function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function generatePrice(basePrice = 25) {
  // 生成 $15-$85 之间的价格
  const variation = Math.random() * 0.6 + 0.7; // 0.7 to 1.3 multiplier
  return Math.round(basePrice * variation * 100); // 转换为 cents
}

async function createPriceSet(client, variantId, price) {
  try {
    const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建 price_set
    await client.query(`
      INSERT INTO price_set (id, created_at, updated_at)
      VALUES ($1, NOW(), NOW())
    `, [priceSetId]);
    
    // 创建 price
    const priceId = `price_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    await client.query(`
      INSERT INTO price (id, price_set_id, currency_code, amount, raw_amount, rules_count, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
    `, [priceId, priceSetId, 'usd', price, JSON.stringify({value: price}), 0]);
    
    // 关联 variant 到 price_set
    const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    await client.query(`
      INSERT INTO product_variant_price_set (id, variant_id, price_set_id, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
    `, [linkId, variantId, priceSetId]);
    
    return priceSetId;
  } catch (error) {
    console.error(`创建价格失败 ${variantId}:`, error.message);
    return null;
  }
}

async function createProductOption(client, productId, optionTitle, values) {
  try {
    const optionId = `opt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建 product_option
    await client.query(`
      INSERT INTO product_option (id, title, product_id, created_at, updated_at)
      VALUES ($1, $2, $3, NOW(), NOW())
    `, [optionId, optionTitle, productId]);
    
    // 创建 product_option_value
    for (const value of values) {
      const valueId = `optval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await client.query(`
        INSERT INTO product_option_value (id, value, option_id, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
      `, [valueId, value.value, optionId]);
    }
    
    return optionId;
  } catch (error) {
    console.error(`创建产品选项失败 ${productId}:`, error.message);
    return null;
  }
}

async function updateProductDescription(client, productId, description, ingredients, shipping) {
  try {
    const metadata = {
      ingredients: ingredients,
      shipping: shipping,
      care_instructions: "Clean with dry cloth only. Avoid direct sunlight and moisture.",
      warranty: "1 year craftsmanship warranty"
    };
    
    await client.query(`
      UPDATE product 
      SET description = $1, metadata = $2, updated_at = NOW()
      WHERE id = $3
    `, [description, JSON.stringify(metadata), productId]);
    
    return true;
  } catch (error) {
    console.error(`更新产品描述失败 ${productId}:`, error.message);
    return false;
  }
}

async function fixMokuomoProductData() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 开始修复 Mokuomo 产品数据...');
    await client.connect();
    console.log('✅ 数据库连接成功');

    // 获取所有 Mokuomo 产品和变体
    const productsResult = await client.query(`
      SELECT p.id as product_id, p.title, p.handle, pv.id as variant_id, pv.title as variant_title
      FROM product p
      INNER JOIN product_variant pv ON p.id = pv.product_id
      WHERE p.deleted_at IS NULL 
      AND pv.deleted_at IS NULL
      AND (p.title LIKE '%Carved%' OR p.title LIKE '%Wooden%')
      ORDER BY p.created_at DESC
    `);
    
    const products = productsResult.rows;
    console.log(`📦 找到 ${products.length} 个需要修复的产品变体`);

    let successCount = 0;
    let failCount = 0;

    for (const product of products) {
      try {
        console.log(`🔧 处理产品: ${product.title}`);
        
        // 1. 检查是否已有价格
        const existingPriceResult = await client.query(`
          SELECT pvps.price_set_id 
          FROM product_variant_price_set pvps 
          WHERE pvps.variant_id = $1 AND pvps.deleted_at IS NULL
        `, [product.variant_id]);
        
        if (existingPriceResult.rows.length === 0) {
          // 创建价格
          const price = generatePrice();
          const priceSetId = await createPriceSet(client, product.variant_id, price);
          if (priceSetId) {
            console.log(`  ✅ 创建价格: $${(price / 100).toFixed(2)}`);
          }
        } else {
          console.log(`  ✅ 价格已存在`);
        }
        
        // 2. 检查是否已有产品选项
        const existingOptionsResult = await client.query(`
          SELECT COUNT(*) as count 
          FROM product_option po 
          WHERE po.product_id = $1 AND po.deleted_at IS NULL
        `, [product.product_id]);
        
        if (parseInt(existingOptionsResult.rows[0].count) === 0) {
          // 创建 Size 选项
          const sizeOptionId = await createProductOption(client, product.product_id, 'Size', sizeOptions);
          if (sizeOptionId) {
            console.log(`  ✅ 创建 Size 选项`);
          }
          
          // 创建 Color 选项
          const colorOptionId = await createProductOption(client, product.product_id, 'Color', colorOptions);
          if (colorOptionId) {
            console.log(`  ✅ 创建 Color 选项`);
          }
        } else {
          console.log(`  ✅ 产品选项已存在`);
        }
        
        // 3. 更新产品描述和元数据
        const description = getRandomElement(mockDescriptions);
        const ingredients = getRandomElement(mockIngredients);
        const shipping = getRandomElement(mockShipping);
        
        const updated = await updateProductDescription(client, product.product_id, description, ingredients, shipping);
        if (updated) {
          console.log(`  ✅ 更新描述和元数据`);
        }
        
        successCount++;
        
        // 添加延迟避免数据库压力
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`❌ 处理产品失败 ${product.title}:`, error.message);
        failCount++;
      }
    }

    console.log('\n📊 修复完成统计:');
    console.log(`✅ 成功: ${successCount} 个产品`);
    console.log(`❌ 失败: ${failCount} 个产品`);
    console.log(`📦 总计: ${products.length} 个产品`);
    
    if (successCount > 0) {
      console.log('\n🎉 Mokuomo 产品数据修复完成！');
      console.log('💡 现在产品应该有价格、选项和完整描述了');
      console.log('💡 建议重新同步产品到 Sanity: node sync-products-to-sanity.js');
    }

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixMokuomoProductData().catch(console.error);
}

module.exports = { fixMokuomoProductData };
