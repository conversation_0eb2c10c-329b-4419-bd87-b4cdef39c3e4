import type {StoreProductParams} from "@medusajs/types";

import {unstable_cache} from "next/cache";

import medusa from "./client";

export const getProductByHandle = unstable_cache(
  async function (handle: string, region_id: string) {
    return medusa.store.product
      .list(
        {
          fields: "*variants.calculated_price,*variants.prices,+variants.inventory_quantity",
          handle,
          region_id,
        },
        {next: {tags: ["products"]}},
      )
      .then(({products}) => products[0]);
  },
  ["product"],
  {
    revalidate: 120,
  },
);

export const getProductsByIds = unstable_cache(
  async function (ids: string[], region_id: string) {
    if (!ids || ids.length === 0) {
      return { products: [] };
    }

    try {
      // 并行获取所有产品，但每个产品单独请求以确保兼容性
      const productPromises = ids.map(async (id) => {
        try {
          const result = await medusa.store.product.list(
            {
              fields: "*variants.calculated_price,*variants.prices,+variants.inventory_quantity,+images.*",
              id: [id], // 单个 ID 作为数组
              region_id,
            },
            { next: { tags: ["products"] } }
          );
          return result.products[0] || null;
        } catch (error) {
          console.warn(`Failed to fetch product ${id}:`, error);
          return null;
        }
      });

      const products = (await Promise.all(productPromises)).filter(Boolean);

      return { products };
    } catch (error) {
      console.error("Error fetching products by IDs:", error);
      return { products: [] };
    }
  },
  ["products-by-ids"],
  {
    revalidate: 60, // 减少缓存时间
  },
);

export const getProducts = unstable_cache(
  async function (
    page: number,
    region_id: string,
    query?: Omit<
      StoreProductParams,
      "fields" | "limit" | "offset" | "region_id"
    >,
  ) {
    const limit = 12;
    const offset = (page - 1) * limit;

    const {count, products} = await medusa.store.product.list(
      {
        fields: "+images.*,+variants.*,*variants.calculated_price",
        limit,
        offset,
        region_id,
        ...query,
      },
      {next: {tags: ["products"]}},
    );

    return {
      hasNextPage: count > offset + limit,
      products,
    };
  },
  ["products"],
  {
    revalidate: 120,
  },
);
