import type {Category} from "@/types/sanity.generated";
import type {StoreProductCategory} from "@medusajs/types";

import {unstable_cache} from "next/cache";

import medusa from "./client";

export const getCategoryByHandle = unstable_cache(
  async function (handle: string[], page: number) {
    const limit = 12;
    const offset = (page - 1) * limit;

    const category = await medusa.store.category
      .list(
        {
          fields: "+sanity_category.*",
          handle: handle[handle.length - 1],
        },
        {next: {tags: ["category"]}},
      )
      .then(
        ({product_categories}) =>
          product_categories[0] as {
            sanity_category: Category;
          } & StoreProductCategory,
      );

    const {count, products} = await medusa.store.product.list(
      {
        category_id: category.id,
        fields: "+images.*,+variants.*",
        limit,
        offset,
      },
      {next: {tags: ["products"]}},
    );

    return {
      category,
      hasNextPage: count > offset + limit,
      products,
    };
  },
  ["category"],
  {
    revalidate: 120,
  },
);

export const getCategories = unstable_cache(
  async function () {
    try {
      // 直接使用 fetch 调用 API
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_MEDUSA_BACKEND_URL}/product-categories?limit=100`,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY!,
          },
          next: { tags: ["categories"], revalidate: 120 },
        }
      );

      if (!response.ok) {
        console.error(`Failed to fetch categories: ${response.status} ${response.statusText}`);
        return { product_categories: [] };
      }

      const data = await response.json();
      console.log('Categories API response:', {
        status: response.status,
        count: data.product_categories?.length || 0,
        categories: data.product_categories?.slice(0, 3).map(c => ({ id: c.id, name: c.name, handle: c.handle })) || []
      });
      return { product_categories: data.product_categories || [] };
    } catch (error) {
      console.error("Error fetching categories:", error);
      return { product_categories: [] };
    }
  },
  ["categories"],
  {
    revalidate: 120,
  },
);
