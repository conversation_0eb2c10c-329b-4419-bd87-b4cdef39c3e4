// 测试价格映射功能
const { getPricesForVariant } = require('./utils/medusa/get-product-price.ts');

// 模拟变体数据
const testVariant = {
  id: 'variant_1755366640268_ae3h859og',
  title: 'Test Variant',
  calculated_price: null, // 模拟 calculated_price 为 null 的情况
};

console.log('测试价格映射功能...');
console.log('变体 ID:', testVariant.id);

try {
  const priceResult = getPricesForVariant(testVariant);
  console.log('价格结果:', priceResult);
  
  if (priceResult) {
    console.log('✅ 价格映射成功');
    console.log('价格:', priceResult.calculated_price);
    console.log('价格类型:', priceResult.price_type);
  } else {
    console.log('❌ 价格映射失败');
  }
} catch (error) {
  console.error('❌ 价格映射出错:', error);
}
