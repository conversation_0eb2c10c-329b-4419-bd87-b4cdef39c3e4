import {defineField} from "sanity";

export default defineField({
  fields: [
    {
      initialValue: "left",
      name: "imagePosition",
      options: {
        layout: "dropdown",
        list: [
          {
            title: "Left",
            value: "left",
          },
          {
            title: "Right",
            value: "right",
          },
        ],
      },
      title: "Image Position",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "image",
      title: "Image",
      type: "image",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "title",
      title: "Title",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "description",
      title: "Description",
      type: "text",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "buttons",
      title: "Action Buttons",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "label",
              title: "Button Label",
              type: "string",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "link",
              title: "Button Link",
              type: "string",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "style",
              title: "Button Style",
              type: "string",
              initialValue: "underline",
              options: {
                layout: "dropdown",
                list: [
                  {title: "Underlined Link", value: "underline"},
                  {title: "Primary Button", value: "primary"},
                  {title: "Secondary Button", value: "secondary"},
                ],
              },
            },
          ],
        },
      ],
      validation: (Rule) => Rule.max(2),
    },
  ],
  name: "section.mediaText",
  preview: {
    prepare: ({image, title}) => ({
      media: image,
      subtitle: "Media + text section",
      title: title,
    }),
    select: {
      image: "image",
      title: "title",
    },
  },
  title: "Media + text section",
  type: "object",
});
