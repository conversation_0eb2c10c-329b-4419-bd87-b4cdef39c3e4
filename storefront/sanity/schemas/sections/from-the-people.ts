import {defineField} from "sanity";

export default defineField({
  fields: [
    {
      name: "title",
      title: "Section Title",
      type: "string",
      initialValue: "From The People",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "testimonials",
      title: "Testimonials",
      type: "array",
      of: [
        {
          type: "object",
          name: "testimonial",
          title: "Testimonial",
          fields: [
            {
              name: "quote",
              title: "Quote",
              type: "text",
              validation: (Rule) => Rule.required(),
              description: "The testimonial text",
            },
            {
              name: "author",
              title: "Author",
              type: "string",
              validation: (Rule) => Rule.required(),
              description: "Name of the person giving the testimonial",
            },
            {
              name: "rating",
              title: "Star Rating",
              type: "number",
              initialValue: 5,
              validation: (Rule) => Rule.min(1).max(5).integer(),
              description: "Star rating from 1 to 5",
            },
            {
              name: "productImage",
              title: "Product Image",
              type: "image",
              options: {
                hotspot: true,
              },
              description: "Image of the product being reviewed",
            },
            {
              name: "productTitle",
              title: "Product Title",
              type: "string",
              description: "Name of the product being reviewed",
            },
            {
              name: "productLink",
              title: "Product Link",
              type: "url",
              description: "Link to the product page",
              validation: (Rule) =>
                Rule.uri({
                  scheme: ["http", "https", "mailto", "tel"],
                }),
            },
          ],
          preview: {
            select: {
              title: "author",
              subtitle: "quote",
              media: "productImage",
            },
            prepare: ({title, subtitle, media}) => ({
              title: title || "Unnamed Author",
              subtitle: subtitle ? `"${subtitle.slice(0, 60)}..."` : "No quote",
              media,
            }),
          },
        },
      ],
      validation: (Rule) => Rule.min(1).max(10),
      description: "Add testimonials with quotes, authors, ratings, and product information",
    },
  ],
  name: "section.fromThePeople",
  preview: {
    prepare: ({title, testimonials}) => ({
      subtitle: `From The People section (${testimonials?.length || 0} testimonials)`,
      title: title || "From The People",
    }),
    select: {
      title: "title",
      testimonials: "testimonials",
    },
  },
  title: "From The People section",
  type: "object",
});
