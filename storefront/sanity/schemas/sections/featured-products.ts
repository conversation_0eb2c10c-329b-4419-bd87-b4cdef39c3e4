import {defineField} from "sanity";

export default defineField({
  fields: [
    {
      name: "title",
      title: "Title",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "products",
      of: [{to: [{type: "product"}], type: "reference"}],
      title: "Products",
      type: "array",
      description: "Select up to 12 products to feature. Products are automatically synced from Medusa.",
      validation: (Rule) => Rule.max(12).warning("Consider limiting to 12 products for better performance"),
      options: {
        layout: "grid",
      },
    },
    {
      name: "cta",
      title: "CTA",
      type: "cta",
    },
  ],
  name: "section.featuredProducts",
  preview: {
    prepare: ({title, products}) => ({
      subtitle: `Featured products section (${products?.length || 0} products)`,
      title: title,
    }),
    select: {
      title: "title",
      products: "products",
    },
  },
  title: "Featured products section",
  type: "object",
});
