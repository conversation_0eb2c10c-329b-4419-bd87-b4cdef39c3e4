import {defineField} from "sanity";

export default defineField({
  fields: [
    {
      name: "quote",
      title: "Quote Text",
      type: "text",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "author",
      title: "Author",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "authorTitle",
      title: "Author Title/Description",
      type: "string",
    },
    {
      name: "backgroundColor",
      title: "Background Style",
      type: "string",
      initialValue: "light",
      options: {
        layout: "dropdown",
        list: [
          {title: "Light Background", value: "light"},
          {title: "Dark Background", value: "dark"},
          {title: "Warm Background", value: "warm"},
        ],
      },
    },
  ],
  name: "section.quote",
  preview: {
    prepare: ({quote, author}) => ({
      subtitle: "Quote section",
      title: `"${quote}" - ${author}`,
    }),
    select: {
      quote: "quote",
      author: "author",
    },
  },
  title: "Quote section",
  type: "object",
});
