import {defineField} from "sanity";

export default defineField({
  fields: [
    {
      name: "title",
      title: "Section Title",
      type: "string",
      initialValue: "Designer Interviews",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "designers",
      title: "Designers",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Designer Name",
              type: "string",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "image",
              title: "Designer Photo",
              type: "image",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "bio",
              title: "Designer Bio",
              type: "text",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "link",
              title: "Interview Link (optional)",
              type: "string",
            },
          ],
          preview: {
            select: {
              title: "name",
              media: "image",
              subtitle: "bio",
            },
          },
        },
      ],
      validation: (Rule) => Rule.min(1).max(6),
    },
  ],
  name: "section.designerInterviews",
  preview: {
    prepare: ({title, designers}) => ({
      subtitle: `Designer interviews section with ${designers?.length || 0} designers`,
      title: title,
    }),
    select: {
      title: "title",
      designers: "designers",
    },
  },
  title: "Designer Interviews",
  type: "object",
});
