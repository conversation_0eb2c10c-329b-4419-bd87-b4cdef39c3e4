import {defineField} from "sanity";

export default defineField({
  fields: [
    {
      name: "title",
      title: "Section Title",
      type: "string",
      initialValue: "Our Partners",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "partners",
      title: "Partners",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Partner Name",
              type: "string",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "logo",
              title: "Partner Logo",
              type: "image",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "backgroundColor",
              title: "Background Color",
              type: "string",
              description: "Hex color code (e.g., #ffffff) or CSS color name",
              initialValue: "#ffffff",
            },
            {
              name: "link",
              title: "Partner Website (optional)",
              type: "string",
            },
          ],
          preview: {
            select: {
              title: "name",
              media: "logo",
              subtitle: "backgroundColor",
            },
          },
        },
      ],
      validation: (Rule) => Rule.min(1).max(12),
    },
  ],
  name: "section.ourPartners",
  preview: {
    prepare: ({title, partners}) => ({
      subtitle: `Partners section with ${partners?.length || 0} partners`,
      title: title,
    }),
    select: {
      title: "title",
      partners: "partners",
    },
  },
  title: "Our Partners",
  type: "object",
});
