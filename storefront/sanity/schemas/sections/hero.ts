import {defineField} from "sanity";

export default defineField({
  fields: [
    {
      initialValue: "image",
      name: "mediaType",
      options: {
        layout: "dropdown",
        list: [
          {title: "Image", value: "image"},
          {title: "Large Image", value: "largeImage"},
          {title: "Video", value: "video"},
          {title: "Carousel", value: "carousel"},
        ],
      },
      title: "Media type",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "title",
      title: "Title",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      hidden: ({parent}) => parent?.mediaType !== "image",
      name: "subtitle",
      title: "Subtitle",
      type: "string",
    },
    {
      name: "cta",
      title: "Call to action",
      type: "cta",
      validation: (Rule) => Rule.required(),
    },
    {
      hidden: ({parent}) => parent?.mediaType !== "image",
      name: "image",
      title: "Image",
      type: "image",
      validation: (Rule) =>
        Rule.custom((value, {parent}) => {
          const parentType = parent as {mediaType?: string};
          return parentType?.mediaType === "image" && !value
            ? "Required"
            : true;
        }),
    },
    {
      hidden: ({parent}) => parent?.mediaType !== "largeImage",
      name: "largeImage",
      title: "Large Image",
      type: "image",
      validation: (Rule) =>
        Rule.custom((value, {parent}) => {
          const parentType = parent as {mediaType?: string};
          return parentType?.mediaType === "largeImage" && !value
            ? "Required"
            : true;
        }),
    },
    {
      hidden: ({parent}) => parent?.mediaType !== "video",
      name: "video",
      title: "Video",
      type: "video",
      validation: (Rule) =>
        Rule.custom((value, {parent}) => {
          const parentType = parent as {mediaType?: string};
          return parentType?.mediaType === "video" && !value
            ? "Required"
            : true;
        }),
    },
    {
      hidden: ({parent}) => parent?.mediaType !== "carousel",
      name: "carouselImages",
      title: "Carousel Images",
      type: "array",
      of: [
        {
          type: "object",
          name: "carouselSlide",
          title: "Carousel Slide",
          fields: [
            {
              name: "image",
              title: "Image",
              type: "image",
              options: {
                hotspot: true,
              },
              validation: (Rule) => Rule.required(),
            },
            {
              name: "title",
              title: "Title",
              type: "string",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "subtitle",
              title: "Subtitle",
              type: "string",
            },
            {
              name: "cta",
              title: "Call to action",
              type: "cta",
            },
          ],
          preview: {
            select: {
              title: "title",
              subtitle: "subtitle",
              media: "image",
            },
            prepare: ({title, subtitle, media}) => ({
              title: title || "Untitled slide",
              subtitle: subtitle || "No subtitle",
              media,
            }),
          },
        },
      ],
      validation: (Rule) =>
        Rule.custom((value, {parent}) => {
          const parentType = parent as {mediaType?: string};
          return parentType?.mediaType === "carousel" && (!value || value.length === 0)
            ? "At least one slide is required for carousel"
            : true;
        }),
      options: {
        layout: "grid",
      },
    },
  ],
  name: "section.hero",
  preview: {
    prepare: ({image, title}) => ({
      media: image,
      subtitle: "Hero section",
      title: title,
    }),
    select: {
      image: "image",
      title: "title",
    },
  },
  title: "Hero section",
  type: "object",
});
