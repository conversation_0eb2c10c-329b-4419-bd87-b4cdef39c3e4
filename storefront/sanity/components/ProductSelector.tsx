import React, { useState, useEffect } from 'react';
import { useClient } from 'sanity';

interface Product {
  _id: string;
  title: string;
  handle: string;
  description?: string;
  thumbnail?: string;
  medusaData?: {
    id: string;
    handle: string;
    material?: string;
    collection_id?: string;
    thumbnail?: string;
    images?: Array<{
      id: string;
      url: string;
    }>;
  };
}

interface ProductSelectorProps {
  value?: { _ref: string; _type: 'reference' }[];
  onChange: (value: { _ref: string; _type: 'reference' }[]) => void;
  maxItems?: number;
}

export default function ProductSelector({ 
  value = [], 
  onChange, 
  maxItems = 10 
}: ProductSelectorProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const client = useClient();

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const query = `*[_type == "product"] | order(title asc) {
        _id,
        title,
        handle,
        description,
        thumbnail,
        medusaData
      }`;
      
      const result = await client.fetch(query);
      setProducts(result);
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = products.filter(product =>
    product.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.handle?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedIds = value.map(item => item._ref);

  const toggleProduct = (productId: string) => {
    const isSelected = selectedIds.includes(productId);
    
    if (isSelected) {
      // 移除产品
      const newValue = value.filter(item => item._ref !== productId);
      onChange(newValue);
    } else {
      // 添加产品（检查最大数量限制）
      if (value.length < maxItems) {
        const newValue = [...value, { _ref: productId, _type: 'reference' as const }];
        onChange(newValue);
      }
    }
  };

  const moveProduct = (fromIndex: number, toIndex: number) => {
    const newValue = [...value];
    const [movedItem] = newValue.splice(fromIndex, 1);
    newValue.splice(toIndex, 0, movedItem);
    onChange(newValue);
  };

  if (loading) {
    return <div className="p-4">Loading products...</div>;
  }

  return (
    <div className="space-y-4">
      {/* 搜索框 */}
      <div>
        <input
          type="text"
          placeholder="Search products..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded"
        />
      </div>

      {/* 已选择的产品 */}
      {value.length > 0 && (
        <div>
          <h4 className="font-semibold mb-2">Selected Products ({value.length}/{maxItems})</h4>
          <div className="space-y-2">
            {value.map((item, index) => {
              const product = products.find(p => p._id === item._ref);
              return (
                <div
                  key={item._ref}
                  className="flex items-center justify-between p-2 bg-blue-50 border border-blue-200 rounded"
                >
                  <div className="flex items-center flex-1">
                    {/* 产品图片 */}
                    <div className="w-12 h-12 mr-3 bg-gray-200 rounded overflow-hidden flex-shrink-0">
                      {(product?.thumbnail || product?.medusaData?.thumbnail || product?.medusaData?.images?.[0]?.url) ? (
                        <img
                          src={product?.thumbnail || product?.medusaData?.thumbnail || product?.medusaData?.images?.[0]?.url}
                          alt={product?.title || 'Product'}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            (e.target as HTMLImageElement).style.display = 'none';
                          }}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                          No Image
                        </div>
                      )}
                    </div>

                    {/* 产品信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{product?.title || 'Unknown Product'}</div>
                      <div className="text-sm text-gray-600 truncate">{product?.handle}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => moveProduct(index, Math.max(0, index - 1))}
                      disabled={index === 0}
                      className="px-2 py-1 text-xs bg-gray-200 rounded disabled:opacity-50"
                    >
                      ↑
                    </button>
                    <button
                      onClick={() => moveProduct(index, Math.min(value.length - 1, index + 1))}
                      disabled={index === value.length - 1}
                      className="px-2 py-1 text-xs bg-gray-200 rounded disabled:opacity-50"
                    >
                      ↓
                    </button>
                    <button
                      onClick={() => toggleProduct(item._ref)}
                      className="px-2 py-1 text-xs bg-red-500 text-white rounded"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* 可选择的产品列表 */}
      <div>
        <h4 className="font-semibold mb-2">Available Products</h4>
        <div className="max-h-64 overflow-y-auto space-y-1">
          {filteredProducts.map((product) => {
            const isSelected = selectedIds.includes(product._id);
            const canSelect = !isSelected && value.length < maxItems;
            
            return (
              <div
                key={product._id}
                className={`p-2 border rounded cursor-pointer transition-colors ${
                  isSelected 
                    ? 'bg-blue-100 border-blue-300' 
                    : canSelect 
                      ? 'hover:bg-gray-50 border-gray-200' 
                      : 'bg-gray-100 border-gray-200 opacity-50 cursor-not-allowed'
                }`}
                onClick={() => canSelect && toggleProduct(product._id)}
              >
                <div className="flex items-center">
                  {/* 产品图片 */}
                  <div className="w-10 h-10 mr-3 bg-gray-200 rounded overflow-hidden flex-shrink-0">
                    {(product.thumbnail || product.medusaData?.thumbnail || product.medusaData?.images?.[0]?.url) ? (
                      <img
                        src={product.thumbnail || product.medusaData?.thumbnail || product.medusaData?.images?.[0]?.url}
                        alt={product.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                        📦
                      </div>
                    )}
                  </div>

                  {/* 产品信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{product.title}</div>
                    <div className="text-sm text-gray-600 truncate">{product.handle}</div>
                    {product.medusaData?.material && (
                      <div className="text-xs text-gray-500 truncate">Material: {product.medusaData.material}</div>
                    )}
                  </div>

                  {/* 选择状态指示器 */}
                  {isSelected && (
                    <div className="ml-2 text-blue-500 font-bold">✓</div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 刷新按钮 */}
      <button
        onClick={fetchProducts}
        className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        Refresh Products
      </button>
    </div>
  );
}
