import { getProductsByIds } from "@/data/medusa/products";
import { getRegion } from "@/data/medusa/regions";

export default async function TestProductsPage({ 
  params 
}: { 
  params: Promise<{ countryCode: string }> 
}) {
  const { countryCode } = await params;
  
  console.log('🧪 Test page - countryCode:', countryCode);
  
  const region = await getRegion(countryCode);
  console.log('🌍 Test page - region:', region ? { id: region.id, name: region.name } : 'null');
  
  if (!region) {
    return <div>No region found for {countryCode}</div>;
  }

  const testIds = [
    'prod_mechwq1vcddlid9fu',
    'prod_mechwq1vialyiepix',
    'prod_mechwq1vn2ffvqu94'
  ];

  console.log('🆔 Test page - testing with IDs:', testIds);
  
  const { products } = await getProductsByIds(testIds, region.id);
  
  console.log('📦 Test page - got products:', products.length);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Product API Test</h1>
      <div className="mb-4">
        <p><strong>Country Code:</strong> {countryCode}</p>
        <p><strong>Region:</strong> {region.name} ({region.id})</p>
        <p><strong>Test IDs:</strong> {testIds.join(', ')}</p>
        <p><strong>Products Found:</strong> {products.length}</p>
      </div>
      
      <div className="grid gap-4">
        {products.map((product: any) => (
          <div key={product.id} className="border p-4 rounded">
            <h3 className="font-semibold">{product.title}</h3>
            <p className="text-sm text-gray-600">ID: {product.id}</p>
            <p className="text-sm text-gray-600">Handle: {product.handle}</p>
            {product.thumbnail && (
              <img 
                src={product.thumbnail} 
                alt={product.title}
                className="w-20 h-20 object-cover mt-2"
              />
            )}
          </div>
        ))}
      </div>
      
      {products.length === 0 && (
        <div className="text-red-500 mt-4">
          No products found! Check the console for debug information.
        </div>
      )}
    </div>
  );
}
