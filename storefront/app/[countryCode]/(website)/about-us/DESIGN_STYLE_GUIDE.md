# Cokugoo About Us - Japanese Minimalist Design Style Guide

## 🎌 设计理念

本页面采用日式极简主义设计风格，灵感来源于 mokuomo.com 的禅意美学，结合木雕工艺的自然特质。

## 🎨 视觉设计原则

### 1. 极简主义 (Minimalism)
- **Less is More**: 去除多余装饰，专注核心内容
- **留白美学**: 大量空白空间营造宁静感
- **简洁线条**: 使用细线和几何元素作装饰

### 2. 禅意平衡 (Zen Balance)
- **对称布局**: 左右交替的内容排列
- **视觉重心**: 居中对齐的主要元素
- **节奏感**: 统一的垂直间距系统

### 3. 文化融合 (Cultural Integration)
- **汉字符号**: 手、木、心、時、鳥、森、創
- **日式引号**: 「」样式的引用设计
- **圆形美学**: 圆点、圆圈装饰元素

## 🎯 组件设计特色

### Hero Section - 禅意入口
```
特色元素：
- 微妙的点阵背景图案
- 细线分割装饰
- 木纹图标占位符
- 全屏高度布局
```

### Core Values - 汉字符号
```
设计亮点：
- 手 (Artisan Soul) - 工匠精神
- 木 (Natural Harmony) - 自然和谐  
- 心 (Enduring Charm) - 持久魅力
- 左右交替布局
- 方形边框容器
```

### Meet Artisans - 圆形头像
```
文化元素：
- 時 (Time) - 传统技艺
- 鳥 (Riley) - 现代设计
- 森 (Arlo) - 自然森林
- 創 (JIOFA) - 创意想象
- 圆形头像设计
- 网格对称布局
```

### Cokugoo Belief - 日式引号
```
禅意设计：
- 「」日式引号样式
- 纯白卡片背景
- 细线装饰元素
- 居中文本布局
```

### Join Story - 极简CTA
```
简洁元素：
- 木纹线条图标
- 纯净按钮设计
- 几何装饰线条
- 清晰行动指引
```

## 🌈 配色系统

### 主色调
- **Primary**: `#ff5227` - 温暖橙色，代表木材的自然色彩
- **Background**: `#ffffff` - 纯白，营造极简空间感
- **Secondary**: `#fff6e6` - 奶油色，温暖的辅助背景

### 透明度层级
- **100%**: 主要标题和重要元素
- **80%**: 副标题和重要文本
- **70%**: 正文内容
- **60%**: 辅助信息
- **50%**: 占位文本
- **40%**: 装饰元素
- **30%**: 分割线
- **20%**: 背景装饰
- **10%**: 微妙背景
- **5%**: 边框线条

## 📐 间距系统

### 垂直节奏
- **Section间距**: 80px (mobile) / 128px (desktop)
- **内容块间距**: 48px (mobile) / 64px (desktop)
- **段落间距**: 24px (mobile) / 32px (desktop)
- **元素间距**: 16px (mobile) / 24px (desktop)

### 水平布局
- **最大宽度**: 1280px (5xl)
- **内容宽度**: 1024px (4xl)
- **文本宽度**: 768px (3xl)
- **侧边距**: 24px (mobile) / 32px (desktop)

## 🔤 字体系统

### 字体族
- **Serif**: 主要字体，用于标题和正文
- **Font Weight**: Normal (400) 为主，Medium (500) 为辅

### 字号层级
- **H1**: 48px (mobile) / 96px (desktop)
- **H2**: 32px (mobile) / 80px (desktop)  
- **H3**: 24px (mobile) / 48px (desktop)
- **Body Large**: 20px (mobile) / 24px (desktop)
- **Body**: 16px (mobile) / 18px (desktop)
- **Small**: 14px (mobile) / 16px (desktop)

## 🎭 交互设计

### 悬停效果
- **微妙缩放**: scale(1.02) 轻微放大
- **颜色变化**: 透明度增加或减少
- **过渡时间**: 300-500ms 平滑过渡
- **缓动函数**: ease-in-out 自然感觉

### 动画原则
- **克制使用**: 避免过度动画
- **功能导向**: 动画服务于用户体验
- **性能优化**: 使用 transform 和 opacity

## 📱 响应式设计

### 断点系统
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px  
- **Desktop**: > 1024px

### 适配策略
- **Mobile First**: 从移动端开始设计
- **渐进增强**: 桌面端增加更多细节
- **保持比例**: 维持设计的视觉平衡

## ✨ 设计亮点

1. **文化融合**: 汉字符号与现代设计的完美结合
2. **禅意美学**: 大量留白营造宁静的浏览体验
3. **对称平衡**: 左右交替布局创造视觉节奏
4. **极简装饰**: 细线、圆点等几何元素点缀
5. **自然色彩**: 温暖橙色呼应木材质感

## 🎯 用户体验目标

- **宁静感**: 让用户感受到禅意和平静
- **专业感**: 体现木雕工艺的专业性
- **文化感**: 传达东方美学的深度
- **现代感**: 保持当代设计的简洁性

---

这种日式极简风格完美契合了木雕工艺的自然本质，同时体现了 mokuomo.com 所倡导的简约美学理念。
