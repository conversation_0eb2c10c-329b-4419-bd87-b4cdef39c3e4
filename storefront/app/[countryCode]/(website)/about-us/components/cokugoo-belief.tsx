import Body from "@/components/shared/typography/body"
import Heading from "@/components/shared/typography/heading"

export default function CokugooBeliefSection() {
  return (
    <section className="py-20 lg:py-32 bg-white relative">
      <div className="max-w-5xl mx-auto px-6 lg:px-8">
        {/* Zen-inspired section header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-8">
            <div className="w-24 h-px bg-text-light"></div>
            <div className="mx-8 w-4 h-4 border border-text-light rounded-full flex items-center justify-center">
              <div className="w-1 h-1 bg-text-muted rounded-full"></div>
            </div>
            <div className="w-24 h-px bg-text-light"></div>
          </div>

          <Heading
            className="text-text-primary mb-12 tracking-tight"
            desktopSize="5xl"
            font="serif"
            mobileSize="2xl"
            tag="h2"
          >
            The Cokugoo Belief: From Nature to Nurture
          </Heading>
        </div>

        {/* Main philosophy content */}
        <div className="text-center mb-20">
          <Body
            className="text-text-primary leading-relaxed max-w-3xl mx-auto"
            desktopSize="2xl"
            font="serif"
            mobileSize="lg"
          >
            We believe that every piece of wood holds a story whispered by the forest. Our mission is to listen to that story. The journey of transforming a raw block into one of our wooden animal figurines is a dialogue with nature itself. We strive to make sustainability and simplicity not just ideas, but a lived reality in every home our art touches.
          </Body>
        </div>

        {/* Minimalist quote section */}
        <div className="relative max-w-4xl mx-auto">
          {/* Subtle background */}
          <div className="bg-background-warm rounded-2xl p-12 lg:p-16 border border-accent-05">
            {/* Quote text with subtle Japanese quote marks */}
            <div className="text-center mb-8">
              <div className="inline-block relative">
                <span className="text-4xl text-text-light font-light absolute -left-6 top-0">「</span>
                <Body
                  className="text-text-primary leading-relaxed italic px-8"
                  desktopSize="2xl"
                  font="serif"
                  mobileSize="xl"
                >
                  Cokugoo is where innovative design meets the soul of wood, creating hand carved wooden animals that spark your imagination and bring natural warmth into everyday life.
                </Body>
                <span className="text-4xl text-text-light font-light absolute -right-6 bottom-0">」</span>
              </div>
            </div>

            {/* Attribution with zen styling */}
            <div className="text-center">
              <div className="w-16 h-px bg-text-light mx-auto mb-4"></div>
              <Body
                className="text-text-secondary font-medium tracking-wide"
                desktopSize="lg"
                font="serif"
                mobileSize="base"
              >
                The Founders of Cokugoo
              </Body>
            </div>
          </div>
        </div>

        {/* Bottom zen elements */}
        <div className="flex justify-center mt-16">
          <div className="flex items-center space-x-3">
            <div className="w-1 h-1 bg-text-light rounded-full"></div>
            <div className="w-8 h-px bg-text-light"></div>
            <div className="w-2 h-2 bg-text-muted rounded-full"></div>
            <div className="w-8 h-px bg-text-light"></div>
            <div className="w-1 h-1 bg-text-light rounded-full"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
