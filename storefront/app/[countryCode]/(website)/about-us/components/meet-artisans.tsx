import Body from "@/components/shared/typography/body";
import Heading from "@/components/shared/typography/heading";

const artisans = [
  {
    description:
      "An elderly master woodcarver with wise, kind eyes and decades of experience. Time brings traditional techniques and deep wisdom to every piece, specializing in classic wooden dragons and intricate details.",
    kanji: "時",
    name: "Time",
    specialty: "Traditional Techniques & Classic Dragons",
  },
  {
    description:
      "A young female designer with a focused, calm expression. <PERSON> works in a bright, modern workshop, creating sleek, abstract wooden bird figurines with smooth curves and contemporary aesthetics.",
    kanji: "鳥",
    name: "<PERSON>",
    specialty: "Modern Design & Abstract Birds",
  },
  {
    description:
      "A ruggedly handsome male artisan in his mid-30s with a gentle beard. <PERSON><PERSON><PERSON> connects deeply with nature, crafting lifelike wooden foxes and forest animals from his rustic, cabin-like workshop.",
    kanji: "森",
    name: "<PERSON><PERSON><PERSON>",
    specialty: "Lifelike Forest Animals",
  },
  {
    description:
      "A creative and energetic designer with a spark of mischief in his eyes. JIOFA brings whimsy and imagination to life, creating unique wooden monster figurines that are both charming and distinctive.",
    kanji: "創",
    name: "<PERSON><PERSON><PERSON>",
    specialty: "Whimsical Creatures & Unique Monsters",
  },
];

export default function MeetArtisans() {
  return (
    <section className="bg-background-warm py-20 lg:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Zen-inspired section header */}
        <div className="mb-20 text-center">
          <div className="mb-8 flex items-center justify-center">
            <div className="h-px w-20 bg-text-light"></div>
            <div className="mx-6 flex space-x-2">
              <div className="h-2 w-2 rounded-full bg-text-muted"></div>
              <div className="h-2 w-2 rounded-full bg-text-light"></div>
              <div className="h-2 w-2 rounded-full bg-text-muted"></div>
            </div>
            <div className="h-px w-20 bg-text-light"></div>
          </div>

          <Heading
            className="mb-8 tracking-tight text-text-primary"
            desktopSize="5xl"
            font="serif"
            mobileSize="2xl"
            tag="h2"
          >
            Meet Our Artisans: The Hands Behind the Art
          </Heading>

          <Body
            className="mx-auto max-w-2xl leading-relaxed text-text-secondary"
            desktopSize="xl"
            font="serif"
            mobileSize="lg"
          >
            The magic of cokugoo lies in the hearts and hands of our dedicated
            designers. Each artisan brings a unique perspective to our
            collective passion for wood carving art.
          </Body>
        </div>

        {/* Minimalist artisans grid */}
        <div className="grid grid-cols-1 gap-12 md:grid-cols-2 lg:gap-16">
          {artisans.map((artisan, index) => (
            <div
              className="group flex flex-col items-center space-y-8 rounded-lg border border-accent-05 bg-white p-8 text-center transition-all duration-500 hover:border-accent-10"
              key={index}
            >
              {/* Minimalist portrait placeholder */}
              <div className="relative">
                <div className="flex h-32 w-32 items-center justify-center rounded-full border border-accent-10 bg-background-warm transition-colors duration-300 group-hover:border-accent-20">
                  <span className="text-4xl font-light text-text-primary">
                    {artisan.kanji}
                  </span>
                </div>
                {/* Subtle accent ring */}
                <div className="absolute inset-0 scale-110 rounded-full border border-accent-05"></div>
              </div>

              {/* Content */}
              <div className="space-y-4">
                <Heading
                  className="tracking-tight text-text-primary"
                  desktopSize="2xl"
                  font="serif"
                  mobileSize="xl"
                  tag="h3"
                >
                  {artisan.name}
                </Heading>

                <Body
                  className="font-medium text-text-tertiary"
                  desktopSize="base"
                  font="serif"
                  mobileSize="sm"
                >
                  {artisan.specialty}
                </Body>

                <Body
                  className="mx-auto max-w-sm leading-relaxed text-text-secondary"
                  desktopSize="base"
                  font="serif"
                  mobileSize="sm"
                >
                  {artisan.description}
                </Body>

                {/* Zen accent line */}
                <div className="pt-4">
                  <div className="mx-auto h-px w-8 bg-text-light"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
