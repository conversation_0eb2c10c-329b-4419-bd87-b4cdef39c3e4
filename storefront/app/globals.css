@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html body[data-scroll-locked] {
    overflow: visible !important;
  }
  body {
    color: var(--text-primary);
    background: var(--background);
    @apply font-serif;
  }
  :root {
    @apply [--header-height:_89px] lg:[--header-height:_108px];
    --max-width: 100vw;
    --min-width: 320px;
    /* Mokuomo-inspired Japanese Minimalist Colors */
    --background: #ffffff;
    --background-warm: #fafafa;
    --accent: #2c2c2c;
    --accent-40: rgba(44, 44, 44, 0.4);
    --accent-20: rgba(44, 44, 44, 0.2);
    --accent-10: rgba(44, 44, 44, 0.1);
    --accent-05: rgba(44, 44, 44, 0.05);
    --secondary: #f8f8f8;
    /* Text Colors for Mokuomo-style Minimalism */
    --text-primary: #2c2c2c;
    --text-secondary: rgba(44, 44, 44, 0.8);
    --text-tertiary: rgba(44, 44, 44, 0.6);
    --text-muted: rgba(44, 44, 44, 0.4);
    --text-light: rgba(44, 44, 44, 0.3);
    --base-font-size: 16px;
  }
  .font-serif {
    --base-font-size: 16px;
  }
  .font-display {
    --base-font-size: 16px;
  }

  .hero-asset {
    min-height: min(590px, calc(100vh - var(--header-height) - 1rem));
  }

  .newletter-text:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
    box-shadow: 0 0 0px 1000px transparent inset !important;
    -webkit-text-fill-color: #2c2c2c !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }

  /* Mokuomo-style custom components */
  .mokuomo-underline-link {
    position: relative;
    display: inline-flex;
    align-items: center;
    font-style: italic;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .mokuomo-underline-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: currentColor;
    transform: scaleX(1);
    transition: transform 0.3s ease;
  }

  .mokuomo-underline-link:hover::after {
    transform: scaleX(0.8);
  }

  .mokuomo-card {
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
  }

  .mokuomo-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(44, 44, 44, 0.1);
  }

  @layer utilities {
    .scrollbar-hide::-webkit-scrollbar {
      display: none;
    }
    .scrollbar-hide {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  }
}
