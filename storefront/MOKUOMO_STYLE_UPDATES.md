# Mokuomo Style Updates

## 概述
我们已经成功将网站的设计风格更新为参考 Mokuomo (https://www.mokuomo.com/) 的日式极简主义风格。

## 主要更新内容

### 1. 颜色系统更新
- **主色调**: 从 `#4a4a4a` 更新为 `#2c2c2c` (更深的灰色)
- **背景色**: 保持纯白 `#ffffff`
- **次要背景**: 更新为 `#f8f8f8` (更温暖的灰白色)
- **文字颜色**: 使用更深的灰色系统，提供更好的对比度

### 2. Hero 组件更新
**文件**: `storefront/components/sections/hero/simple-hero.tsx`, `large-hero.tsx`

**更新内容**:
- 添加了 Mokuomo 风格的标语 "Craft • Wood • Nature"
- 改进了布局和间距
- 添加了渐变覆盖层（大型 Hero）
- 更好的视觉层次结构

### 3. Media + Text 组件重新设计
**文件**: `storefront/components/sections/media-text.tsx`, `storefront/sanity/schemas/sections/media-text.ts`

**新功能**:
- 支持多个按钮配置（最多2个）
- 三种按钮样式：
  - 下划线链接（Mokuomo 风格）
  - 主要按钮
  - 次要按钮
- 改进的布局和排版
- 更好的图片比例（4:3）

### 4. 新增 Quote 组件
**文件**: `storefront/components/sections/quote.tsx`, `storefront/sanity/schemas/sections/quote.ts`

**功能**:
- 显示引用文字和作者
- 三种背景样式：浅色、深色、温暖色调
- 响应式设计
- 优雅的排版

### 5. 产品卡片更新
**文件**: `storefront/components/shared/product-card.tsx`

**改进**:
- 添加了悬停效果和动画
- 图片缩放效果
- 更好的阴影和圆角
- Mokuomo 风格的卡片类

### 6. 按钮系统扩展
**文件**: `storefront/components/shared/button.tsx`

**新增**:
- 次要按钮变体 (`secondary`)
- 改进的样式系统

### 7. Testimonials 组件重新设计
**文件**: `storefront/components/sections/testimonials.tsx`

**更新**:
- Mokuomo 风格的卡片设计
- 改进的排版和间距
- 温暖的背景色调

### 8. Featured Products 组件优化
**文件**: `storefront/components/sections/featured-products.tsx`

**改进**:
- 左对齐标题（符合 Mokuomo 风格）
- 更好的间距和布局

### 9. 新增 Designer Interviews 组件
**文件**: `storefront/components/sections/designer-interviews.tsx`, `storefront/sanity/schemas/sections/designer-interviews.ts`

**功能**:
- 展示设计师访谈
- 支持设计师照片、姓名、简介
- 可选的访谈链接
- 响应式网格布局（1-3列）
- 悬停效果和动画

### 10. 新增 Our Partners 组件
**文件**: `storefront/components/sections/our-partners.tsx`, `storefront/sanity/schemas/sections/our-partners.ts`

**功能**:
- 展示合作伙伴品牌
- 支持自定义背景颜色
- 合作伙伴 Logo 和名称
- 可选的合作伙伴网站链接
- 响应式网格布局（2-6列）
- Mokuomo 风格的卡片设计

### 11. 自定义 CSS 样式
**文件**: `storefront/app/globals.css`

**新增样式**:
- `.mokuomo-underline-link`: 下划线链接样式
- `.mokuomo-card`: 卡片悬停效果
- 更新的颜色变量

## 设计原则

### Mokuomo 风格特点
1. **极简主义**: 干净、简洁的设计
2. **自然色调**: 温暖的灰色和白色
3. **优雅排版**: 使用衬线字体作为主要标题
4. **微妙动画**: 轻柔的悬停效果和过渡
5. **日式美学**: 注重空白和平衡

### 响应式设计
- 所有组件都支持移动端和桌面端
- 使用 Tailwind CSS 的响应式类
- 优化的图片比例和间距

## CMS 配置

### 新的组件选项
1. **Media + Text**: 现在支持按钮配置
2. **Quote**: 新的引用组件
3. **Designer Interviews**: 设计师访谈展示
4. **Our Partners**: 合作伙伴品牌展示
5. **Hero**: 改进的样式选项

### 按钮配置选项
- **标签**: 按钮文字
- **链接**: 目标 URL
- **样式**:
  - 下划线链接（推荐用于 Mokuomo 风格）
  - 主要按钮
  - 次要按钮

### Designer Interviews 配置
- **标题**: 可自定义的节标题
- **设计师列表**: 最多6个设计师
  - 设计师姓名
  - 设计师照片
  - 设计师简介
  - 访谈链接（可选）

### Our Partners 配置
- **标题**: 可自定义的节标题
- **合作伙伴列表**: 最多12个合作伙伴
  - 合作伙伴名称
  - 合作伙伴 Logo
  - 背景颜色（支持十六进制颜色代码）
  - 合作伙伴网站链接（可选）

## 技术实现

### 类型生成
- 已更新 Sanity 类型定义
- 支持新的 Quote 组件
- 扩展的 Media + Text 组件类型

### 性能优化
- 使用 CSS 变量进行主题管理
- 优化的图片加载
- 高效的组件渲染

## 下一步建议

1. **内容更新**: 在 CMS 中添加 Mokuomo 风格的内容
2. **图片优化**: 使用高质量的产品图片
3. **SEO 优化**: 更新元数据和描述
4. **测试**: 在不同设备上测试响应式设计

## 访问链接
- 前端: http://localhost:3002/us
- CMS: http://localhost:3000/cms

现在网站具有了 Mokuomo 的优雅、极简风格，同时保持了完整的功能性和可配置性。
