import type {Header} from "@/types/sanity.generated";

import Cart from "./cart";
import HeaderWrapper from "./header-wrapper";

export default function Header(props: {countryCode: string} & Header) {
  const cartComponent = (
    <Cart
      cartAddons={props.cartAddons}
      countryCode={props.countryCode}
    />
  );

  return (
    <HeaderWrapper {...props} cartComponent={cartComponent} />
  );
}
