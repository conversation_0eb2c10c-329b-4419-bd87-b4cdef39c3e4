import type {Head<PERSON>} from "@/types/sanity.generated";

import Icon from "@/components/shared/icon";
import LocalizedLink from "@/components/shared/localized-link";
import {Suspense} from "react";

import Cart from "./cart";
import AnnouncementBar from "./parts/announcement-bar";
import BottomBorder from "./parts/bottom-border";
import HamburgerContainer from "./parts/hamburger/container";
import HeaderWrapper from "./header-wrapper";

export default function Header(props: {countryCode: string} & Header) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    handleScroll();
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <header
      className={cx(
        "fixed top-0 z-50 flex w-full flex-col items-center transition-all duration-300",
        {
          "bg-transparent": !isScrolled,
          "bg-background shadow-sm": isScrolled,
        }
      )}
    >
      <AnnouncementBar {...props} />
      <div className="mx-auto flex w-full max-w-max-screen items-center justify-between gap-2xl px-m py-xs lg:px-xl">
        <div className="flex items-center gap-m">
          <div className="flex items-center justify-start gap-s">
            <HamburgerContainer sanityData={props} />
            <LocalizedLink href="/" prefetch>
              {/* <img
                alt="Mubchies logo"
                className="my-[9px] h-[22px] w-fit lg:my-[10px] lg:h-9"
                src="/images/logo.svg"
              /> */}
             <span
               className={cx(
                 "text-2xl max-md:text-base transition-colors duration-300",
                 {
                   "text-white": !isScrolled,
                   "text-text-primary": isScrolled,
                 }
               )}
             >
               Wood Carving
             </span>
            </LocalizedLink>
          </div>
          <Suspense>
            <Navigation data={props} isScrolled={isScrolled} />
          </Suspense>
        </div>
        <div className="flex items-center gap-s">
          <span className="hidden lg:block">
            <CountrySelectorWrapper isScrolled={isScrolled} />
          </span>
          <Suspense
            fallback={
              <div className="relative h-10 w-10 p-2">
                <Icon
                  name="Cart"
                  className={cx(
                    "transition-colors duration-300",
                    {
                      "text-white": !isScrolled,
                      "text-text-primary": isScrolled,
                    }
                  )}
                />
              </div>
            }
          >
            <Cart
              cartAddons={props.cartAddons}
              countryCode={props.countryCode}
              isScrolled={isScrolled}
            />
          </Suspense>
        </div>
      </div>
      <div className="relative z-30 w-screen" id="navigation-portal" />

      <BottomBorder className="lg:hidden" />
    </header>
  );
}
