"use client";

import {useEffect, useState} from "react";
import {type Header} from "@/types/sanity.generated";

import type {Country} from "../../country-selector/country-selector-dialog";
import Hamburger from ".";

export default function HamburgerContainerWrapper({
  sanityData,
}: {
  sanityData: Header;
}) {
  const [countries, setCountries] = useState<Country[]>([]);

  useEffect(() => {
    // Load countries from API route
    fetch("/api/countries")
      .then((res) => res.json())
      .then((data) => {
        setCountries(data as Country[]);
      })
      .catch((error) => {
        console.error("Error loading countries:", error);
        setCountries([]);
      });
  }, []);

  return <Hamburger countries={countries} data={sanityData} />;
}
