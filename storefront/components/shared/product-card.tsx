import type {StoreProduct} from "@medusajs/types";

import {getProductPrice} from "@/utils/medusa/get-product-price";
import {cx} from "cva";
import Image from "next/image";
import Icon from "./icon";
import LocalizedLink from "./localized-link";
import Tag from "./tag";
import Body from "./typography/body";

export default function ProductCard({
  index,
  product,
  size = "default",
}: {
  index?: number;
  product: StoreProduct | undefined;
  size?: "PLP" | "default";
}) {
  if (!product) return null;

  const {cheapestPrice} = getProductPrice({product});

  const thumbnail = product.thumbnail || product.images?.[0]?.url;

  return (
    <LocalizedLink
      className={cx(
        "group flex flex-1 flex-col items-center justify-center rounded-lg mokuomo-card transition-all duration-300",
        {
          "w-[88vw] max-w-[450px]": size === "default",
        },
      )}
      href={`/products/${product?.handle}`}
      prefetch
    >
      <div className="relative w-full overflow-hidden rounded-lg">
        {thumbnail ? (
          <Image
            alt={product.title}
            className="aspect-square w-full rounded-lg object-cover transition-transform duration-300 group-hover:scale-105"
            height={450}
            priority={index !== undefined && index <= 2}
            src={thumbnail}
            width={450}
          />
        ): <>
          <div className="aspect-square w-full rounded-lg bg-secondary" >
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
              <Icon
                className="size-10 animate-spin-loading"
                name="LoadingAccent"
              />
            </div>
          </div>
        </>}
        {product.type?.value && (
          <Tag
            className="absolute right-4 top-3"
            text={product.type.value || ""}
          />
        )}
      </div>

      <div className="pointer-events-none flex flex-1 flex-col items-center justify-center gap-2 px-4 py-4">
        <Body
          className="text-center font-medium transition-colors group-hover:text-text-secondary"
          desktopSize="lg"
          font="serif"
          mobileSize="base"
        >
          {product.title}
        </Body>
        <Body
          className="text-center text-text-secondary"
          desktopSize="base"
          font="sans"
          mobileSize="sm"
        >
          {cheapestPrice?.calculated_price ? `${cheapestPrice.calculated_price}` : "Price on request"}
        </Body>
      </div>
    </LocalizedLink>
  );
}
