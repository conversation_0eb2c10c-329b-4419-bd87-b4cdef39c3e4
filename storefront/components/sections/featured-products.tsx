import {getProductsByIds} from "@/data/medusa/products";
import {getRegion} from "@/data/medusa/regions";

import type {ModularPageSection} from "./types";

import MokuomoFeaturedProducts from "./featured-products/mokuomo-featured-products";

export default async function FeaturedProducts(
  props: ModularPageSection<"section.featuredProducts">,
) {
  const region = await getRegion(props.countryCode);

  if (!region) {
    console.log("No region found");
    return null;
  }

  const ids =
    props.products
      ?.map((product) => product?._ref)
      .filter((id): id is string => id !== undefined) || [];

  if (!ids || ids.length === 0) return null;

  const {products} = await getProductsByIds(ids, region.id);

  const validProducts = products.filter((product): product is NonNullable<typeof product> =>
    Boolean(product && product.id)
  );

  return (
    <MokuomoFeaturedProducts
      {...props}
      products={validProducts}
    />
  );
}
