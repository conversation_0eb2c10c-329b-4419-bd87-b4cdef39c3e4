import {cx} from "cva";
import {stega<PERSON>lean} from "next-sanity";
import React from "react";

import type {ModularPageSection} from "./types";

import Body from "../shared/typography/body";
import Label from "../shared/typography/label";

export default function Quote(
  props: ModularPageSection<"section.quote">,
) {
  const backgroundColor = stegaClean(props.backgroundColor) || "light";
  
  return (
    <section
      {...props.rootHtmlAttributes}
      className={cx(
        "w-full px-4 py-2xl lg:px-8 lg:py-3xl",
        {
          "bg-background": backgroundColor === "light",
          "bg-accent text-white": backgroundColor === "dark",
          "bg-background-warm": backgroundColor === "warm",
        }
      )}
    >
      <div className="mx-auto max-w-4xl text-center">
        <Body
          className={cx(
            "mb-8 text-balance italic leading-relaxed",
            {
              "text-text-primary": backgroundColor !== "dark",
              "text-white": backgroundColor === "dark",
            }
          )}
          desktopSize="4xl"
          font="serif"
          mobileSize="2xl"
        >
          "{props.quote}"
        </Body>
        
        <div className="flex flex-col items-center gap-2">
          <Label
            className={cx(
              "font-medium",
              {
                "text-text-primary": backgroundColor !== "dark",
                "text-white": backgroundColor === "dark",
              }
            )}
            desktopSize="lg"
            font="sans"
            mobileSize="base"
          >
            ~ {props.author} ~
          </Label>
          
          {props.authorTitle && (
            <Label
              className={cx(
                {
                  "text-text-secondary": backgroundColor !== "dark",
                  "text-white/80": backgroundColor === "dark",
                }
              )}
              desktopSize="base"
              font="sans"
              mobileSize="sm"
            >
              {props.authorTitle}
            </Label>
          )}
        </div>
      </div>
    </section>
  );
}
