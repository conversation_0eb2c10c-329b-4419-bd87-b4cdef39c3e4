"use client";

import type {StoreProduct} from "@medusajs/types";
import {cx} from "cva";
import {useState} from "react";

import type {ModularPageSection} from "../types";

import MokuomoProductCard from "./mokuomo-product-card";

type Props = Omit<ModularPageSection<"section.featuredProducts">, 'products'> & {
  products: StoreProduct[];
};

export default function MokuomoFeaturedProducts({
  products,
  title,
  cta,
  rootHtmlAttributes,
}: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const itemsPerPage = 4;
  const totalPages = Math.ceil(products.length / itemsPerPage);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % totalPages);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + totalPages) % totalPages);
  };

  const currentProducts = products.slice(
    currentIndex * itemsPerPage,
    (currentIndex + 1) * itemsPerPage
  );

  return (
    <section
      {...rootHtmlAttributes}
      className="w-full bg-white py-16"
    >
      <div className="container mx-auto px-4">
        {/* Mokuomo-style title with navigation */}
        <div className="mb-12 flex items-center justify-center">
          <button
            onClick={prevSlide}
            className="mr-8 flex h-8 w-8 items-center justify-center text-gray-400 transition-colors hover:text-gray-600"
            disabled={totalPages <= 1}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>

          <div className="text-center">
            <h2 className="text-2xl font-normal text-gray-800 lg:text-3xl">
              {title}
            </h2>
          </div>

          <button
            onClick={nextSlide}
            className="ml-8 flex h-8 w-8 items-center justify-center text-gray-400 transition-colors hover:text-gray-600"
            disabled={totalPages <= 1}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>

        {/* Products grid */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {currentProducts.map((product, index) => (
            <MokuomoProductCard
              key={product.id}
              product={product}
              index={index}
            />
          ))}
        </div>

        {/* CTA Link */}
        {cta?.link && cta?.label && (
          <div className="mt-12 text-center">
            <a
              href={cta.link}
              className="inline-flex items-center text-lg font-medium text-gray-800 underline decoration-1 underline-offset-4 transition-colors hover:text-gray-600"
            >
              {cta.label}
            </a>
          </div>
        )}
      </div>
    </section>
  );
}
