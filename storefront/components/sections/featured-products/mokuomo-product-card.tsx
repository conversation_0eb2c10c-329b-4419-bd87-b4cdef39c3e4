"use client";

import type {StoreProduct} from "@medusajs/types";
import {getProductPrice} from "@/utils/medusa/get-product-price";
import {cx} from "cva";
import Image from "next/image";
import {useState} from "react";

import LocalizedLink from "../../shared/localized-link";

type Props = {
  product: StoreProduct;
  index: number;
};

export default function MokuomoProductCard({product, index}: Props) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  
  const {cheapestPrice} = getProductPrice({product});
  const images = product.images || [];
  const thumbnail = product.thumbnail || images[0]?.url;
  
  const nextImage = () => {
    if (images.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % images.length);
    }
  };

  const prevImage = () => {
    if (images.length > 1) {
      setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
    }
  };

  const currentImage = images[currentImageIndex]?.url || thumbnail;

  // Calculate discount percentage if there's a compare price
  const hasDiscount = cheapestPrice?.original_price && 
    cheapestPrice.calculated_price && 
    cheapestPrice.original_price > cheapestPrice.calculated_price;
  
  const discountPercentage = hasDiscount 
    ? Math.round(((cheapestPrice.original_price - cheapestPrice.calculated_price) / cheapestPrice.original_price) * 100)
    : 0;

  return (
    <LocalizedLink
      href={`/products/${product.handle}`}
      className="group block"
      prefetch
    >
      <div 
        className="relative overflow-hidden bg-gray-50"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Product Image */}
        <div className="relative aspect-square w-full">
          {currentImage ? (
            <Image
              src={currentImage}
              alt={product.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              priority={index <= 3}
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-gray-100">
              <span className="text-gray-400">No image</span>
            </div>
          )}

          {/* Discount Badge */}
          {hasDiscount && (
            <div className="absolute left-3 top-3 bg-white px-2 py-1 text-xs font-medium text-gray-800">
              {discountPercentage}% off
            </div>
          )}

          {/* Image Navigation Arrows */}
          {images.length > 1 && isHovered && (
            <>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  prevImage();
                }}
                className="absolute left-2 top-1/2 flex h-8 w-8 -translate-y-1/2 items-center justify-center bg-white/80 text-gray-600 opacity-0 transition-opacity group-hover:opacity-100"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  nextImage();
                }}
                className="absolute right-2 top-1/2 flex h-8 w-8 -translate-y-1/2 items-center justify-center bg-white/80 text-gray-600 opacity-0 transition-opacity group-hover:opacity-100"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </button>
            </>
          )}

          {/* Quick Buy Button */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 opacity-0 transition-opacity group-hover:opacity-100">
            <button className="bg-white px-4 py-2 text-sm font-medium text-gray-800 transition-colors hover:bg-gray-100">
              Quick buy
            </button>
          </div>
        </div>

        {/* Product Info */}
        <div className="p-4 text-center">
          <h3 className="mb-2 text-sm font-medium text-gray-800 group-hover:text-gray-600">
            {product.title}
          </h3>
          <div className="flex items-center justify-center gap-2">
            {hasDiscount && (
              <span className="text-sm text-gray-400 line-through">
                {cheapestPrice.original_price}
              </span>
            )}
            <span className={cx("text-sm font-medium", {
              "text-red-600": hasDiscount,
              "text-gray-800": !hasDiscount,
            })}>
              {cheapestPrice?.calculated_price || "Price on request"}
            </span>
            {hasDiscount && (
              <span className="text-xs text-gray-600">Sale</span>
            )}
          </div>
        </div>
      </div>
    </LocalizedLink>
  );
}
