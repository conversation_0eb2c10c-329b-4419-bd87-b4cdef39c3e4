import {cx} from "cva";
import {stega<PERSON>lean} from "next-sanity";
import React from "react";

import type {ModularPageSection} from "./types";

import {Link} from "../shared/button";
import {SanityImage} from "../shared/sanity-image";
import Body from "../shared/typography/body";
import Heading from "../shared/typography/heading";

export default function MediaText(
  props: ModularPageSection<"section.mediaText">,
) {
  const position = stegaClean(props.imagePosition);
  return (
    <section
      {...props.rootHtmlAttributes}
      className={cx(
        "flex w-full flex-col items-stretch justify-center gap-8 px-4 py-xl lg:px-8 lg:py-2xl",
        {
          "lg:flex-row": position === "right",
          "lg:flex-row-reverse": position === "left",
        },
      )}
    >
      <div className="flex flex-col items-start justify-center gap-6 lg:w-1/2 lg:pr-8">
        <Heading
          className="text-left"
          desktopSize="3xl"
          font="serif"
          mobileSize="2xl"
          tag="h2"
        >
          {props.title}
        </Heading>
        <Body
          className="text-left leading-relaxed"
          desktopSize="lg"
          font="sans"
          mobileSize="base"
        >
          {props.description}
        </Body>
        {props.buttons && props.buttons.length > 0 && (
          <div className="flex flex-col gap-4 sm:flex-row sm:gap-6">
            {props.buttons.map((button, index) => {
              if (button.style === "underline") {
                return (
                  <a
                    key={index}
                    href={button.link}
                    className="group relative inline-flex items-center text-text-primary transition-colors hover:text-text-secondary"
                  >
                    <span className="font-serif text-lg font-medium italic">
                      {button.label}
                    </span>
                    <div className="ml-2 h-px flex-1 bg-text-primary transition-colors group-hover:bg-text-secondary" />
                  </a>
                );
              }
              return (
                <Link
                  key={index}
                  href={button.link}
                  size="md"
                  variant={button.style === "primary" ? "primary" : "secondary"}
                >
                  {button.label}
                </Link>
              );
            })}
          </div>
        )}
      </div>
      {props.image ? (
        <div className="aspect-[4/3] rounded-lg lg:w-1/2">
          <SanityImage
            alt={props.title || "Media image"}
            className="h-full w-full rounded-lg object-cover"
            data={props.image}
          />
        </div>
      ) : (
        <div className="aspect-[4/3] rounded-lg bg-secondary lg:w-1/2" />
      )}
    </section>
  );
}
