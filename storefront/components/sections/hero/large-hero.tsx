import {Link} from "@/components/shared/button";
import Heading from "@/components/shared/typography/heading";
import Label from "@/components/shared/typography/label";
import React from "react";

import type {ModularPageSection} from "../types";

export default function LargeHero({
  children,
  props,
}: {
  children?: React.ReactNode;
  props: ModularPageSection<"section.hero">;
}) {
  return (
    <div className="relative aspect-[16/9] overflow-hidden rounded-lg">
      {children}
      {/* Mokuomo-style overlay with gradient */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

      <div className="absolute bottom-0 left-1/2 z-10 flex w-full -translate-x-1/2 flex-col items-center justify-center gap-6 text-balance px-s py-2xl text-center lg:max-w-[680px] lg:py-6xl">
        {/* Mokuomo-style tagline */}
        <Label
          className="tracking-wider text-white/90"
          desktopSize="base"
          font="sans"
          mobileSize="sm"
        >
          Craft • Wood • Nature
        </Label>

        <Heading
          className="!leading-[110%] text-white drop-shadow-lg"
          desktopSize="6xl"
          font="serif"
          mobileSize="4xl"
          tag="h1"
        >
          {props.title}
        </Heading>

        {props.cta?.link && (
          <Link
            href={props.cta.link}
            prefetch
            size="lg"
            variant="primary"
            className="mt-4 shadow-lg"
          >
            {props.cta.label}
          </Link>
        )}
      </div>
    </div>
  );
}
