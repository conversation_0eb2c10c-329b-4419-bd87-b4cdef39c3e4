import {Link} from "@/components/shared/button";
import {SanityImage} from "@/components/shared/sanity-image";
import Body from "@/components/shared/typography/body";
import Heading from "@/components/shared/typography/heading";
import Label from "@/components/shared/typography/label";
import {stegaClean} from "next-sanity";

import type {ModularPageSection} from "../types";

export default function SimpleHero(props: ModularPageSection<"section.hero">) {
  const image = stegaClean(props.image);
  return (
    <div className="flex flex-col items-stretch justify-center gap-8 lg:flex-row-reverse lg:gap-12">
      <div className="flex min-h-[500px] w-full flex-col items-center justify-center gap-8 px-m py-8xl text-center lg:w-1/2 lg:py-m">
        {/* Mokuomo-style tagline */}
        <Label
          className="tracking-wider text-text-secondary"
          desktopSize="base"
          font="sans"
          mobileSize="sm"
        >
          Craft • Wood • Nature
        </Label>

        <Heading
          className="!leading-[110%] text-balance"
          desktopSize="5xl"
          font="serif"
          mobileSize="3xl"
          tag="h1"
        >
          {props.title}
        </Heading>

        {props.cta?.link && (
          <Link
            className="mt-4"
            href={props.cta.link}
            prefetch
            size="lg"
            variant="primary"
          >
            {props.cta.label}
          </Link>
        )}
      </div>
      {image ? (
        <div className="aspect-[4/3] rounded-lg lg:w-1/2">
          <SanityImage
            alt={props.title || "Hero image"}
            className="h-full w-full rounded-lg object-cover object-center"
            data={image}
            fetchPriority="high"
          />
        </div>
      ) : (
        <div className="aspect-[4/3] rounded-lg bg-secondary lg:w-1/2" />
      )}
    </div>
  );
}
