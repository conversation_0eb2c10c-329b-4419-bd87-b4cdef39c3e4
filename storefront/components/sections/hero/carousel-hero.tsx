"use client";

import {SanityImage} from "@/components/shared/sanity-image";
import {cx} from "cva";
import {useState, useEffect} from "react";

import type {ModularPageSection} from "../types";

import LargeHero from "./large-hero";

type Props = ModularPageSection<"section.hero">;

export default function CarouselHero(props: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const images = props.carouselImages || [];

  // Auto-advance carousel every 5 seconds
  useEffect(() => {
    if (images.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [images.length]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <LargeHero props={props}>
      <div className="hero-asset relative w-full overflow-hidden rounded-lg">
        {/* Carousel Images */}
        <div className="relative h-full w-full">
          {images.map((image, index) => (
            <div
              key={index}
              className={cx(
                "absolute inset-0 transition-opacity duration-500",
                {
                  "opacity-100": index === currentIndex,
                  "opacity-0": index !== currentIndex,
                }
              )}
            >
              <SanityImage
                className="h-full w-full object-cover object-center"
                data={image}
                fetchPriority={index === 0 ? "high" : "low"}
              />
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-white/80 text-gray-800 transition-all hover:bg-white hover:scale-110"
              aria-label="Previous image"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-white/80 text-gray-800 transition-all hover:bg-white hover:scale-110"
              aria-label="Next image"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </>
        )}

        {/* Dots Indicator */}
        {images.length > 1 && (
          <div className="absolute bottom-6 left-1/2 z-10 flex -translate-x-1/2 space-x-2">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cx(
                  "h-3 w-3 rounded-full transition-all",
                  {
                    "bg-white": index === currentIndex,
                    "bg-white/50 hover:bg-white/75": index !== currentIndex,
                  }
                )}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Gradient Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
      </div>
    </LargeHero>
  );
}
