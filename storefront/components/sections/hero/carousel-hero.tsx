"use client";

import {Link} from "@/components/shared/button";
import {SanityImage} from "@/components/shared/sanity-image";
import Heading from "@/components/shared/typography/heading";
import Label from "@/components/shared/typography/label";
import {cx} from "cva";
import {useState, useEffect} from "react";

import type {ModularPageSection} from "../types";

type Props = ModularPageSection<"section.hero">;

export default function CarouselHero(props: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const slides = props.carouselImages || [];

  // Auto-advance carousel every 5 seconds
  useEffect(() => {
    if (slides.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (!slides || slides.length === 0) {
    return null;
  }

  const currentSlide = slides[currentIndex];

  return (
    <div className="relative aspect-[16/9] overflow-hidden rounded-lg">
      <div className="hero-asset relative w-full overflow-hidden rounded-lg">
        {/* Carousel Images */}
        <div className="relative h-full w-full">
          {slides.map((slide, index) => (
            <div
              key={index}
              className={cx(
                "absolute inset-0 transition-opacity duration-500",
                {
                  "opacity-100": index === currentIndex,
                  "opacity-0": index !== currentIndex,
                }
              )}
            >
              <SanityImage
                className="h-full w-full object-cover object-center"
                data={slide.image}
                fetchPriority={index === 0 ? "high" : "low"}
              />
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        {slides.length > 1 && (
          <>
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-white/80 text-gray-800 transition-all hover:bg-white hover:scale-110"
              aria-label="Previous image"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-white/80 text-gray-800 transition-all hover:bg-white hover:scale-110"
              aria-label="Next image"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </>
        )}

        {/* Dots Indicator */}
        {slides.length > 1 && (
          <div className="absolute bottom-6 left-1/2 z-10 flex -translate-x-1/2 space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cx(
                  "h-3 w-3 rounded-full transition-all",
                  {
                    "bg-white": index === currentIndex,
                    "bg-white/50 hover:bg-white/75": index !== currentIndex,
                  }
                )}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Mokuomo-style overlay with gradient */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

      <div className="absolute bottom-0 left-1/2 z-10 flex w-full -translate-x-1/2 flex-col items-center justify-center gap-6 text-balance px-s py-2xl text-center lg:max-w-[680px] lg:py-6xl">
        {/* Mokuomo-style tagline */}
        <Label
          className="tracking-wider text-white/90"
          desktopSize="base"
          font="sans"
          mobileSize="sm"
        >
          Craft • Wood • Nature
        </Label>

        <Heading
          className="!leading-[110%] text-white drop-shadow-lg"
          desktopSize="6xl"
          font="serif"
          mobileSize="4xl"
          tag="h1"
        >
          {currentSlide?.title || props.title}
        </Heading>

        {currentSlide?.subtitle && (
          <Label
            className="text-white/80 drop-shadow-lg"
            desktopSize="lg"
            font="sans"
            mobileSize="base"
          >
            {currentSlide.subtitle}
          </Label>
        )}

        {currentSlide?.cta?.link && (
          <Link
            href={currentSlide.cta.link}
            prefetch
            size="lg"
            variant="primary"
            className="mt-4 shadow-lg"
          >
            {currentSlide.cta.label}
          </Link>
        )}
      </div>
    </div>
  );
}
