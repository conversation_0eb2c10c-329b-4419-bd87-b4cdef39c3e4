import type {SectionList} from "./types";

import Assurance from "./assurance";
import CenteredText from "./centered-text";
import CollectionList from "./collection-list";
import DesignerInterviews from "./designer-interviews";
import FeaturedProducts from "./featured-products";
import FromThePeople from "./from-the-people";
import <PERSON> from "./hero";
import Marquee from "./marquee";
import MediaText from "./media-text";
import OurPartners from "./our-partners";
import Quote from "./quote";
import ShopTheLook from "./shopTheLook";
import Testimonials from "./testimonials";

export const sectionsList: SectionList = {
  "section.assurance": Assurance,
  "section.centeredText": CenteredText,
  "section.collectionList": CollectionList,
  "section.designerInterviews": DesignerInterviews,
  "section.featuredProducts": FeaturedProducts,
  "section.fromThePeople": FromThePeople,
  "section.hero": <PERSON>,
  "section.marquee": <PERSON>que<PERSON>,
  "section.mediaText": MediaText,
  "section.ourPartners": OurPartners,
  "section.quote": Quote,
  "section.shopTheLook": ShopThe<PERSON>ook,
  "section.testimonials": Testimonials,
};
