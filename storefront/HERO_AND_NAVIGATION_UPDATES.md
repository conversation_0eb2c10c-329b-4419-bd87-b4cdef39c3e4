# Hero 和导航栏更新

## 概述
根据用户需求，我们对首页 hero 组件和导航栏进行了重大更新，以匹配 Mo<PERSON><PERSON> (https://www.mokuomo.com/) 的设计风格。

## 主要更新内容

### 1. Hero Carousel 组件增强

#### 更新的文件：
- `storefront/sanity/schemas/sections/hero.ts`
- `storefront/components/sections/hero/carousel-hero.tsx`

#### 新功能：
- **独立的幻灯片配置**：每个轮播图片现在可以配置独立的：
  - 标题 (title)
  - 副标题 (subtitle) 
  - 行动按钮 (CTA)
- **改进的数据结构**：从简单的图片数组改为包含完整内容的幻灯片对象
- **Mokuomo 风格的覆盖层**：添加了渐变覆盖层以提高文字可读性
- **动态内容显示**：根据当前幻灯片显示相应的标题、副标题和按钮

#### Schema 更改：
```typescript
// 之前：简单的图片数组
carouselImages: [{ type: "image" }]

// 现在：完整的幻灯片对象
carouselImages: [{
  type: "object",
  fields: [
    { name: "image", type: "image" },
    { name: "title", type: "string" },
    { name: "subtitle", type: "string" },
    { name: "cta", type: "cta" }
  ]
}]
```

### 2. 导航栏 Mokuomo 风格更新

#### 更新的文件：
- `storefront/components/global/header/index.tsx`
- `storefront/components/global/header/parts/navigation.tsx`
- `storefront/components/global/header/country-selector/country-selector-wrapper.tsx`
- `storefront/components/global/header/cart/index.tsx`
- `storefront/components/global/header/cart/cart-ui.tsx`
- `storefront/components/global/header/cart/open-cart-button.tsx`
- `storefront/app/[countryCode]/(website)/layout.tsx`

#### 新功能：
- **固定定位**：导航栏现在使用 `fixed` 定位而不是 `sticky`
- **透明背景**：页面顶部时导航栏背景透明
- **滚动响应**：页面滚动时背景变为白色，文字颜色相应调整
- **平滑过渡**：所有颜色变化都有 300ms 的过渡动画

#### 颜色状态：
- **未滚动状态**：
  - 背景：透明 (`bg-transparent`)
  - 文字：白色 (`text-white`)
  - 边框：白色半透明 (`border-white/30`)

- **滚动状态**：
  - 背景：白色 (`bg-background`)
  - 文字：主题色 (`text-text-primary`)
  - 边框：主题色 (`border-accent`)
  - 阴影：轻微阴影 (`shadow-sm`)

### 3. 技术实现细节

#### 滚动检测：
```typescript
const [isScrolled, setIsScrolled] = useState(false);

useEffect(() => {
  const handleScroll = () => {
    setIsScrolled(window.scrollY > 0);
  };
  
  handleScroll();
  window.addEventListener("scroll", handleScroll);
  return () => window.removeEventListener("scroll", handleScroll);
}, []);
```

#### 条件样式：
```typescript
className={cx(
  "fixed top-0 z-50 flex w-full flex-col items-center transition-all duration-300",
  {
    "bg-transparent": !isScrolled,
    "bg-background shadow-sm": isScrolled,
  }
)}
```

### 4. 布局调整

#### 主内容区域：
- 添加了 `pt-[var(--header-height)]` 以补偿固定导航栏的高度
- 确保内容不被导航栏遮挡

### 5. 组件架构改进

#### 新增组件：
- `CountrySelectorWrapper`：客户端包装器，支持滚动状态传递

#### Props 传递：
- 所有导航栏子组件现在接收 `isScrolled` 属性
- 统一的颜色状态管理

## 使用说明

### 配置 Hero Carousel：
1. 在 Sanity Studio 中选择 "Carousel" 媒体类型
2. 为每个幻灯片配置：
   - 上传图片
   - 设置标题
   - 添加副标题（可选）
   - 配置行动按钮

### 导航栏行为：
- 页面加载时导航栏透明，文字为白色
- 用户滚动页面时自动切换为白色背景，文字为深色
- 所有交互元素（链接、按钮、图标）都会相应调整颜色

## 兼容性

- 所有更改向后兼容
- 现有的 hero 配置继续工作
- 新的 carousel 功能为可选增强

## 下一步

建议测试以下场景：
1. 不同设备上的导航栏响应性
2. Hero carousel 的图片加载性能
3. 滚动时的动画流畅性
4. 各种内容长度下的文字显示效果
