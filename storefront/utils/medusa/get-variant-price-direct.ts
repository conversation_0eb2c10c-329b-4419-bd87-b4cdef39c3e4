// 直接从数据库获取变体价格的工具函数
// 这是一个临时解决方案，用于绕过 Medusa v2 的价格计算问题

interface VariantPriceData {
  variant_id: string;
  amount: number;
  currency_code: string;
  formatted_price: string;
}

// 缓存价格数据
const priceCache = new Map<string, VariantPriceData>();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

export async function getVariantPriceDirect(variantId: string): Promise<VariantPriceData | null> {
  // 检查缓存
  const cached = priceCache.get(variantId);
  if (cached) {
    return cached;
  }

  try {
    // 调用后端 API 获取价格
    const response = await fetch(`http://localhost:9000/admin/variants/${variantId}/price`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.warn(`Failed to fetch price for variant ${variantId}: ${response.status}`);
      return null;
    }

    const data = await response.json();
    
    if (data && data.amount) {
      const priceData: VariantPriceData = {
        variant_id: variantId,
        amount: data.amount,
        currency_code: data.currency_code || 'usd',
        formatted_price: `$${(data.amount / 100).toFixed(2)}`,
      };

      // 缓存结果
      priceCache.set(variantId, priceData);
      
      // 设置缓存过期
      setTimeout(() => {
        priceCache.delete(variantId);
      }, CACHE_TTL);

      return priceData;
    }
  } catch (error) {
    console.error(`Error fetching price for variant ${variantId}:`, error);
  }

  return null;
}

// 批量获取多个变体的价格
export async function getVariantPricesBatch(variantIds: string[]): Promise<Map<string, VariantPriceData>> {
  const results = new Map<string, VariantPriceData>();
  
  // 并行获取所有价格
  const promises = variantIds.map(async (variantId) => {
    const price = await getVariantPriceDirect(variantId);
    if (price) {
      results.set(variantId, price);
    }
  });

  await Promise.all(promises);
  return results;
}

// 为产品变体添加价格信息的辅助函数
export function enrichVariantWithPrice(variant: any, priceData: VariantPriceData | null) {
  if (!priceData) {
    return variant;
  }

  // 创建一个模拟的 calculated_price 对象
  const mockCalculatedPrice = {
    calculated_amount: priceData.amount,
    original_amount: priceData.amount,
    currency_code: priceData.currency_code,
    calculated_price: {
      price_list_type: 'default',
    },
  };

  return {
    ...variant,
    calculated_price: mockCalculatedPrice,
    // 也添加到其他可能的价格字段
    price: {
      amount: priceData.amount,
      currency_code: priceData.currency_code,
    },
    prices: [{
      amount: priceData.amount,
      currency_code: priceData.currency_code,
    }],
  };
}
