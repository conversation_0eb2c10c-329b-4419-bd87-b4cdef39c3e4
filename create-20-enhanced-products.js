const { Client } = require('pg');

// 数据库连接配置
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'medusa_dev',
  user: process.env.USER || 'lilsnake',
});

// 生成唯一ID的函数
function generateId(prefix = '') {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}${random}`;
}

// 随机选择函数
function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// 随机价格生成
function randomPrice(min, max) {
  return (Math.random() * (max - min) + min).toFixed(2);
}

// 20个增强版木雕产品数据
const enhancedProducts = [
  {
    name: '威武神龙雕刻',
    material: '红木',
    sizeInches: '12×8×6',
    weightGrams: 2500,
    isNew: true,
    isBestseller: false,
    designer: '张大师',
    designConcept: '传承千年工艺，融合现代美学，展现东方文化的深厚底蕴',
    usageScenario: '客厅装饰、书房摆设、茶室点缀',
    craftsmanship: '传统手工雕刻、精细打磨、天然漆艺',
    deliveryScope: '全国包邮，3-7个工作日送达，支持货到付款'
  },
  {
    name: '祥瑞凤凰摆件',
    material: '檀木',
    sizeInches: '10×6×8',
    weightGrams: 1800,
    isNew: false,
    isBestseller: true,
    designer: '李工匠',
    designConcept: '以自然为师，用匠心雕琢，诠释生活中的诗意与美好',
    usageScenario: '办公室装饰、会议室摆件、接待区展示',
    craftsmanship: '立体圆雕技法、镂空雕刻、表面抛光',
    deliveryScope: '国内顺丰快递，48小时内发货，包装精美'
  },
  {
    name: '福禄寿三星',
    material: '黄花梨',
    sizeInches: '8×4×10',
    weightGrams: 2200,
    isNew: false,
    isBestseller: true,
    designer: '王艺人',
    designConcept: '古法新韵，精工细作，每一刀都承载着工匠的情怀',
    usageScenario: '卧室装饰、床头柜摆件、私人收藏',
    craftsmanship: '浮雕工艺、阴刻技法、手工上色',
    deliveryScope: '全球配送，海外7-15个工作日，专业包装'
  },
  {
    name: '观音菩萨像',
    material: '紫檀',
    sizeInches: '6×4×12',
    weightGrams: 1500,
    isNew: false,
    isBestseller: false,
    designer: '陈雕师',
    designConcept: '天人合一的哲学思想，通过木雕艺术传达内心的宁静',
    usageScenario: '禅修空间、冥想室、静心场所',
    craftsmanship: '榫卯结构、无胶拼接、传统工艺',
    deliveryScope: '同城当日达，异地次日达，保价运输'
  },
  {
    name: '梅兰竹菊四君子',
    material: '楠木',
    sizeInches: '16×2×8',
    weightGrams: 1200,
    isNew: true,
    isBestseller: false,
    designer: '刘木匠',
    designConcept: '简约而不简单，在朴素中见真章，体现禅意生活',
    usageScenario: '书房收藏、文人雅士、学者书斋',
    craftsmanship: '精雕细琢、层次分明、立体感强',
    deliveryScope: '专业物流，全程跟踪，安全送达'
  },
  {
    name: '荷花莲蓬雅韵',
    material: '椴木',
    sizeInches: '14×10×6',
    weightGrams: 2000,
    isNew: false,
    isBestseller: true,
    designer: '赵师傅',
    designConcept: '传统技艺与创新设计的完美结合，打造独特的艺术品',
    usageScenario: '餐厅装饰、茶台摆设、家庭聚会',
    craftsmanship: '刀法娴熟、线条流畅、造型生动',
    deliveryScope: '包邮配送，专业包装，破损包赔'
  },
  {
    name: '现代抽象艺术品',
    material: '乌木',
    sizeInches: '8×8×12',
    weightGrams: 1600,
    isNew: true,
    isBestseller: false,
    designer: '孙艺术家',
    designConcept: '师法自然，取材天然，展现木材本身的纹理之美',
    usageScenario: '艺术收藏、投资收藏、传家之宝',
    craftsmanship: '古法制作、纯手工艺、匠心独运',
    deliveryScope: '快递配送，安全可靠，签收确认'
  },
  {
    name: '简约线条雕塑',
    material: '花梨木',
    sizeInches: '6×6×15',
    weightGrams: 1400,
    isNew: true,
    isBestseller: false,
    designer: '周大师',
    designConcept: '匠心独运，精雕细琢，每件作品都是独一无二的艺术品',
    usageScenario: '客厅装饰、书房摆设、茶室点缀',
    craftsmanship: '精工细作、注重细节、完美呈现',
    deliveryScope: '物流配送，全程保险，放心购买'
  },
  {
    name: '创意动物造型',
    material: '酸枝木',
    sizeInches: '10×8×8',
    weightGrams: 1800,
    isNew: false,
    isBestseller: true,
    designer: '吴工艺师',
    designConcept: '传承千年工艺，融合现代美学，展现东方文化的深厚底蕴',
    usageScenario: '办公室装饰、会议室摆件、接待区展示',
    craftsmanship: '传统手工雕刻、精细打磨、天然漆艺',
    deliveryScope: '全国包邮，3-7个工作日送达，支持货到付款'
  },
  {
    name: '几何拼接艺术',
    material: '鸡翅木',
    sizeInches: '12×12×4',
    weightGrams: 2200,
    isNew: true,
    isBestseller: false,
    designer: '郑雕刻家',
    designConcept: '以自然为师，用匠心雕琢，诠释生活中的诗意与美好',
    usageScenario: '卧室装饰、床头柜摆件、私人收藏',
    craftsmanship: '立体圆雕技法、镂空雕刻、表面抛光',
    deliveryScope: '国内顺丰快递，48小时内发货，包装精美'
  },
  {
    name: '精美茶具套装',
    material: '红木',
    sizeInches: '16×12×8',
    weightGrams: 3000,
    isNew: false,
    isBestseller: true,
    designer: '张大师',
    designConcept: '古法新韵，精工细作，每一刀都承载着工匠的情怀',
    usageScenario: '餐厅装饰、茶台摆设、家庭聚会',
    craftsmanship: '浮雕工艺、阴刻技法、手工上色',
    deliveryScope: '全球配送，海外7-15个工作日，专业包装'
  },
  {
    name: '文房四宝盒',
    material: '檀木',
    sizeInches: '14×10×6',
    weightGrams: 2500,
    isNew: false,
    isBestseller: false,
    designer: '李工匠',
    designConcept: '天人合一的哲学思想，通过木雕艺术传达内心的宁静',
    usageScenario: '书房收藏、文人雅士、学者书斋',
    craftsmanship: '榫卯结构、无胶拼接、传统工艺',
    deliveryScope: '同城当日达，异地次日达，保价运输'
  },
  {
    name: '香薰炉座',
    material: '黄花梨',
    sizeInches: '8×8×10',
    weightGrams: 1200,
    isNew: true,
    isBestseller: false,
    designer: '王艺人',
    designConcept: '简约而不简单，在朴素中见真章，体现禅意生活',
    usageScenario: '禅修空间、冥想室、静心场所',
    craftsmanship: '精雕细琢、层次分明、立体感强',
    deliveryScope: '专业物流，全程跟踪，安全送达'
  },
  {
    name: '珠宝首饰盒',
    material: '紫檀',
    sizeInches: '12×8×6',
    weightGrams: 1800,
    isNew: false,
    isBestseller: true,
    designer: '陈雕师',
    designConcept: '传统技艺与创新设计的完美结合，打造独特的艺术品',
    usageScenario: '卧室装饰、床头柜摆件、私人收藏',
    craftsmanship: '刀法娴熟、线条流畅、造型生动',
    deliveryScope: '包邮配送，专业包装，破损包赔'
  },
  {
    name: '古典花瓶架',
    material: '楠木',
    sizeInches: '10×10×20',
    weightGrams: 2800,
    isNew: false,
    isBestseller: false,
    designer: '刘木匠',
    designConcept: '师法自然，取材天然，展现木材本身的纹理之美',
    usageScenario: '客厅装饰、书房摆设、茶室点缀',
    craftsmanship: '古法制作、纯手工艺、匠心独运',
    deliveryScope: '快递配送，安全可靠，签收确认'
  },
  {
    name: '禅意水景摆件',
    material: '椴木',
    sizeInches: '18×12×8',
    weightGrams: 3500,
    isNew: true,
    isBestseller: true,
    designer: '赵师傅',
    designConcept: '匠心独运，精雕细琢，每件作品都是独一无二的艺术品',
    usageScenario: '禅修空间、冥想室、静心场所',
    craftsmanship: '精工细作、注重细节、完美呈现',
    deliveryScope: '物流配送，全程保险，放心购买'
  },
  {
    name: '山水意境雕刻',
    material: '乌木',
    sizeInches: '20×6×12',
    weightGrams: 2800,
    isNew: false,
    isBestseller: true,
    designer: '孙艺术家',
    designConcept: '传承千年工艺，融合现代美学，展现东方文化的深厚底蕴',
    usageScenario: '艺术收藏、投资收藏、传家之宝',
    craftsmanship: '传统手工雕刻、精细打磨、天然漆艺',
    deliveryScope: '全国包邮，3-7个工作日送达，支持货到付款'
  },
  {
    name: '书法艺术屏风',
    material: '花梨木',
    sizeInches: '24×2×18',
    weightGrams: 4000,
    isNew: false,
    isBestseller: false,
    designer: '周大师',
    designConcept: '以自然为师，用匠心雕琢，诠释生活中的诗意与美好',
    usageScenario: '办公室装饰、会议室摆件、接待区展示',
    craftsmanship: '立体圆雕技法、镂空雕刻、表面抛光',
    deliveryScope: '国内顺丰快递，48小时内发货，包装精美'
  },
  {
    name: '现代灯具底座',
    material: '酸枝木',
    sizeInches: '8×8×12',
    weightGrams: 1600,
    isNew: true,
    isBestseller: false,
    designer: '吴工艺师',
    designConcept: '古法新韵，精工细作，每一刀都承载着工匠的情怀',
    usageScenario: '客厅装饰、书房摆设、茶室点缀',
    craftsmanship: '浮雕工艺、阴刻技法、手工上色',
    deliveryScope: '全球配送，海外7-15个工作日，专业包装'
  },
  {
    name: '艺术收纳盒',
    material: '鸡翅木',
    sizeInches: '10×8×4',
    weightGrams: 1200,
    isNew: true,
    isBestseller: false,
    designer: '郑雕刻家',
    designConcept: '天人合一的哲学思想，通过木雕艺术传达内心的宁静',
    usageScenario: '卧室装饰、床头柜摆件、私人收藏',
    craftsmanship: '榫卯结构、无胶拼接、传统工艺',
    deliveryScope: '同城当日达，异地次日达，保价运输'
  }
];

async function create20EnhancedProducts() {
  try {
    await client.connect();
    console.log('🔌 已连接到数据库');

    // 1. 获取现有的分类和集合
    console.log('📋 获取现有分类和集合...');
    
    const categoriesResult = await client.query('SELECT id, handle FROM product_category WHERE deleted_at IS NULL');
    const collectionsResult = await client.query('SELECT id, handle FROM product_collection WHERE deleted_at IS NULL');
    
    const categoryMap = {};
    categoriesResult.rows.forEach(row => {
      categoryMap[row.handle] = row.id;
    });
    
    const collectionMap = {};
    collectionsResult.rows.forEach(row => {
      collectionMap[row.handle] = row.id;
    });
    
    console.log(`  📁 找到 ${categoriesResult.rows.length} 个分类`);
    console.log(`  📦 找到 ${collectionsResult.rows.length} 个集合`);

    // 2. 创建20个增强版产品
    console.log('🎨 创建20个增强版木雕产品...');
    
    for (let i = 0; i < enhancedProducts.length; i++) {
      const product = enhancedProducts[i];
      const productId = generateId('prod');
      
      // 价格计算
      const purchasePriceCny = randomPrice(200, 2000);
      const salePriceUsd = (purchasePriceCny * 0.2).toFixed(2); // 约1:7的汇率加上利润
      
      // 选择集合
      let collectionId = null;
      if (product.isNew && collectionMap['new-arrivals']) {
        collectionId = collectionMap['new-arrivals'];
      } else if (product.isBestseller && collectionMap['best-sellers']) {
        collectionId = collectionMap['best-sellers'];
      } else if (collectionMap['limited-edition']) {
        collectionId = collectionMap['limited-edition'];
      }

      // 生成唯一的 handle
      const baseHandle = product.name.toLowerCase()
        .replace(/[^\u4e00-\u9fa5\w\s]/g, '') // 保留中文、字母、数字、空格
        .replace(/\s+/g, '-') // 空格替换为连字符
        .replace(/^-+|-+$/g, ''); // 去除首尾连字符

      const handle = `${baseHandle}-${i + 1}`;

      // 创建产品
      await client.query(`
        INSERT INTO product (
          id, title, handle, description, material, design_concept, usage_scenario,
          craftsmanship, delivery_scope, designer_name, is_new_product, is_bestseller,
          purchase_price_cny, sale_price_usd, size_inches, weight_grams,
          status, collection_id, thumbnail, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [
        productId, product.name, handle,
        `精美的${product.name}，采用${product.material}精心雕刻而成，展现传统工艺与现代美学的完美结合。`,
        product.material, product.designConcept, product.usageScenario, product.craftsmanship,
        product.deliveryScope, product.designer, product.isNew, product.isBestseller,
        purchasePriceCny, salePriceUsd, product.sizeInches, product.weightGrams,
        'published', collectionId, `https://images.unsplash.com/photo-${1500000000000 + i}?w=400`
      ]);

      // 创建产品变体
      const variantId = generateId('variant');
      const timestamp = Date.now().toString().slice(-6); // 使用时间戳后6位确保唯一性
      const sku = `WC${String(i + 1).padStart(3, '0')}-${product.material.substring(0, 2).toUpperCase()}-${timestamp}`;
      
      await client.query(`
        INSERT INTO product_variant (
          id, title, product_id, sku, manage_inventory, allow_backorder,
          weight, length, width, height, material, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [
        variantId, 'Default', productId, sku, false, false,
        product.weightGrams / 1000, // 转换为公斤
        parseFloat(product.sizeInches.split('×')[0]),
        parseFloat(product.sizeInches.split('×')[1]),
        parseFloat(product.sizeInches.split('×')[2]),
        product.material
      ]);

      // 随机关联一个现有分类
      const availableCategories = Object.keys(categoryMap);
      if (availableCategories.length > 0) {
        const randomCategory = randomChoice(availableCategories);
        const categoryId = categoryMap[randomCategory];
        
        await client.query(`
          INSERT INTO product_category_product (product_id, product_category_id)
          VALUES ($1, $2)
          ON CONFLICT (product_id, product_category_id) DO NOTHING
        `, [productId, categoryId]);
      }

      console.log(`  ✅ 创建产品 ${i + 1}/20: ${product.name} (${product.designer}作品) - ¥${purchasePriceCny}/$${salePriceUsd}`);
    }

    console.log('\n🎉 20个增强版木雕产品创建完成！');
    console.log('\n📊 数据统计:');
    console.log(`  🎨 新增产品: ${enhancedProducts.length} 个`);
    console.log(`  👨‍🎨 涉及设计师: ${[...new Set(enhancedProducts.map(p => p.designer))].length} 位`);
    console.log(`  🪵 使用材料: ${[...new Set(enhancedProducts.map(p => p.material))].length} 种`);
    console.log(`  🆕 新品数量: ${enhancedProducts.filter(p => p.isNew).length} 个`);
    console.log(`  🔥 畅销品数量: ${enhancedProducts.filter(p => p.isBestseller).length} 个`);
    
    console.log('\n🔍 可以通过以下方式查看数据:');
    console.log('  管理后台: http://localhost:9000/app/login');
    console.log('  完整产品信息: SELECT * FROM product_full_info ORDER BY created_at DESC LIMIT 20;');
    
  } catch (error) {
    console.error('❌ 创建数据时出错:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行脚本
create20EnhancedProducts();
