#!/bin/bash

echo "设置 Medusa 开发数据库..."

# 添加 PostgreSQL 到 PATH
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 创建数据库
createdb medusa_dev 2>/dev/null || echo "数据库 medusa_dev 已存在"

# 测试数据库连接
psql -d medusa_dev -c "SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败，请检查 PostgreSQL 是否正在运行"
    echo "运行: brew services start postgresql@15"
    exit 1
fi

# 测试 Redis 连接
redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis 连接成功"
else
    echo "❌ Redis 连接失败，请检查 Redis 是否正在运行"
    echo "运行: brew services start redis"
    exit 1
fi

echo "✅ 数据库设置完成！"
