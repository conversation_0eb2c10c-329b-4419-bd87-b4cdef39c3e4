-- 更新产品表结构以支持图片中的商品属性
-- Wood Carving 项目产品字段扩展

-- 1. 为产品表添加新字段
ALTER TABLE product ADD COLUMN IF NOT EXISTS design_concept TEXT; -- 设计理念
ALTER TABLE product ADD COLUMN IF NOT EXISTS usage_scenario TEXT; -- 使用场景
ALTER TABLE product ADD COLUMN IF NOT EXISTS craftsmanship TEXT; -- 手艺和技能
ALTER TABLE product ADD COLUMN IF NOT EXISTS delivery_scope TEXT; -- 订单配送范围
ALTER TABLE product ADD COLUMN IF NOT EXISTS designer_name TEXT; -- 设计师名称
ALTER TABLE product ADD COLUMN IF NOT EXISTS is_new_product BOOLEAN DEFAULT FALSE; -- 是否新品
ALTER TABLE product ADD COLUMN IF NOT EXISTS is_bestseller BOOLEAN DEFAULT FALSE; -- 是否畅销品
ALTER TABLE product ADD COLUMN IF NOT EXISTS purchase_price_cny DECIMAL(10,2); -- 采购价格（人民币）
ALTER TABLE product ADD COLUMN IF NOT EXISTS sale_price_usd DECIMAL(10,2); -- 销售价格（美金）
ALTER TABLE product ADD COLUMN IF NOT EXISTS size_inches TEXT; -- 产品尺寸（英寸）
ALTER TABLE product ADD COLUMN IF NOT EXISTS weight_grams INTEGER; -- 重量（克）

-- 2. 为产品分类表添加层级字段
ALTER TABLE product_category ADD COLUMN IF NOT EXISTS category_level INTEGER DEFAULT 1; -- 分类层级 (1=一级, 2=二级, 3=三级)
ALTER TABLE product_category ADD COLUMN IF NOT EXISTS category_path TEXT; -- 分类路径，如 "木雕艺术品/动物雕刻/野生动物"

-- 3. 创建产品分类层级视图，方便查询三级分类
CREATE OR REPLACE VIEW product_category_hierarchy AS
WITH RECURSIVE category_tree AS (
    -- 一级分类（根分类）
    SELECT 
        id,
        name,
        handle,
        parent_category_id,
        1 as level,
        name as path,
        name as level1_name,
        NULL::text as level2_name,
        NULL::text as level3_name
    FROM product_category 
    WHERE parent_category_id IS NULL AND deleted_at IS NULL
    
    UNION ALL
    
    -- 递归查找子分类
    SELECT 
        c.id,
        c.name,
        c.handle,
        c.parent_category_id,
        ct.level + 1,
        ct.path || ' / ' || c.name,
        ct.level1_name,
        CASE WHEN ct.level + 1 = 2 THEN c.name ELSE ct.level2_name END,
        CASE WHEN ct.level + 1 = 3 THEN c.name ELSE ct.level3_name END
    FROM product_category c
    JOIN category_tree ct ON c.parent_category_id = ct.id
    WHERE c.deleted_at IS NULL
)
SELECT 
    id,
    name,
    handle,
    parent_category_id,
    level,
    path,
    level1_name,
    level2_name,
    level3_name
FROM category_tree;

-- 4. 创建产品完整信息视图
CREATE OR REPLACE VIEW product_full_info AS
SELECT 
    p.id,
    p.title as chinese_name,
    p.handle,
    p.description,
    p.material,
    p.design_concept,
    p.usage_scenario,
    p.craftsmanship,
    p.delivery_scope,
    p.designer_name,
    p.is_new_product,
    p.is_bestseller,
    p.purchase_price_cny,
    p.sale_price_usd,
    p.size_inches,
    p.weight_grams,
    p.thumbnail as product_image,
    p.status,
    p.created_at,
    p.updated_at,
    
    -- 产品变体信息
    pv.sku as sku_code,
    pv.weight as weight_numeric,
    pv.length,
    pv.width,
    pv.height,
    
    -- 分类信息
    pch.level1_name,
    pch.level2_name,
    pch.level3_name,
    pch.path as category_path,
    
    -- 集合信息
    pc.title as collection_name,
    pc.handle as collection_handle
    
FROM product p
LEFT JOIN product_variant pv ON p.id = pv.product_id
LEFT JOIN product_category_product pcp ON p.id = pcp.product_id
LEFT JOIN product_category_hierarchy pch ON pcp.product_category_id = pch.id
LEFT JOIN product_collection pc ON p.collection_id = pc.id
WHERE p.deleted_at IS NULL;

-- 5. 更新现有产品分类的层级信息
UPDATE product_category 
SET category_level = 1, category_path = name
WHERE parent_category_id IS NULL AND deleted_at IS NULL;

UPDATE product_category 
SET category_level = 2, 
    category_path = (
        SELECT parent.name || ' / ' || product_category.name 
        FROM product_category parent 
        WHERE parent.id = product_category.parent_category_id
    )
WHERE parent_category_id IS NOT NULL AND deleted_at IS NULL;

-- 6. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_product_is_new ON product(is_new_product) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_product_is_bestseller ON product(is_bestseller) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_product_designer ON product(designer_name) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_product_category_level ON product_category(category_level) WHERE deleted_at IS NULL;

-- 7. 添加注释
COMMENT ON COLUMN product.design_concept IS '设计理念';
COMMENT ON COLUMN product.usage_scenario IS '使用场景';
COMMENT ON COLUMN product.craftsmanship IS '手艺和技能';
COMMENT ON COLUMN product.delivery_scope IS '订单配送范围';
COMMENT ON COLUMN product.designer_name IS '设计师名称';
COMMENT ON COLUMN product.is_new_product IS '是否新品';
COMMENT ON COLUMN product.is_bestseller IS '是否畅销品';
COMMENT ON COLUMN product.purchase_price_cny IS '采购价格（人民币）';
COMMENT ON COLUMN product.sale_price_usd IS '销售价格（美金）';
COMMENT ON COLUMN product.size_inches IS '产品尺寸（英寸）';
COMMENT ON COLUMN product.weight_grams IS '重量（克）';
COMMENT ON COLUMN product_category.category_level IS '分类层级 (1=一级, 2=二级, 3=三级)';
COMMENT ON COLUMN product_category.category_path IS '分类路径';

-- 完成提示
SELECT 'Product schema update completed successfully!' as status;
