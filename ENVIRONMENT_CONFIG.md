# 🔧 环境变量配置说明

## 问题解决

### 管理后台登录域名问题

**问题**: 管理后台登录时请求的域名是 `https://munchies.medusajs.app/auth/user/emailpass` 而不是本地开发环境

**解决方案**: 在环境变量和配置文件中正确设置后端 URL

## 📁 配置文件位置

### 后端环境变量 (`backend/.env`)
```env
# 基础配置
NODE_ENV=development
DATABASE_URL=postgresql://localhost:5432/medusa_dev
REDIS_URL=redis://localhost:6379
JWT_SECRET=supersecret
COOKIE_SECRET=supersecret
BACKEND_URL=http://localhost:9000
MEDUSA_BACKEND_URL=http://localhost:9000
MEDUSA_ADMIN_BACKEND_URL=http://localhost:9000

# CORS 配置
STORE_CORS=http://localhost:3000,http://localhost:3001,http://localhost:3002
ADMIN_CORS=http://localhost:7000,http://localhost:7001
AUTH_CORS=http://localhost:7000,http://localhost:7001

# Sanity 配置
SANITY_API_TOKEN="sk7e8ed8JSEHUu9T7w4t5iZiwwBztstYhqEiSb1d5hprDOy5W2ngaMukPrAhIdcRTaV2b9MXwZOZ6iZqPV7yJ5EJj1jTB5DwNHZoTjtmuxOIQDsatycDeJZPTH8iTdvs5dcPUdSm1r91Q23zmFv7dAe3IjG89X2x8LiNAmWeqxPa32ThoHrQ"
SANITY_PROJECT_ID="qqvccmla"
SANITY_ORGANIZATION_ID="ohsoWcUsW"

# Stripe 配置
STRIPE_API_KEY=sk_test_51NlStnKq7bhPXtx0YmpbX2oaVHDtYayKKUFVaY0VQH54TWyDRxVmhU5Mtqk6x9dGhQzb1KqWfzcYCcHPCldpFdOG00BdISoZ49

# 邮件服务
RESEND_API_KEY=re_test_placeholder_key_for_development
```

### 前端环境变量 (`storefront/.env`)
```env
# Medusa 后端配置
NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000/store

# Sanity CMS 配置
NEXT_PUBLIC_SANITY_PROJECT_ID="qqvccmla"
NEXT_PUBLIC_SANITY_DATASET="production"
SANITY_API_TOKEN="sk7e8ed8JSEHUu9T7w4t5iZiwwBztstYhqEiSb1d5hprDOy5W2ngaMukPrAhIdcRTaV2b9MXwZOZ6iZqPV7yJ5EJj1jTB5DwNHZoTjtmuxOIQDsatycDeJZPTH8iTdvs5dcPUdSm1r91Q23zmFv7dAe3IjG89X2x8LiNAmWeqxPa32ThoHrQ"
NEXT_PUBLIC_SANITY_API_VERSION="2025-08-15"

# 其他配置
NODE_ENV=development
```

### Medusa 配置文件 (`backend/medusa-config.ts`)
```typescript
admin: {
  backendUrl: process.env.MEDUSA_ADMIN_BACKEND_URL || "http://localhost:9000",
},
```

## 🔑 关键环境变量说明

### 后端 URL 配置
- `BACKEND_URL`: 后端服务的基础 URL
- `MEDUSA_BACKEND_URL`: Medusa 后端 URL（用于 API 调用）
- `MEDUSA_ADMIN_BACKEND_URL`: 管理后台后端 URL（**关键**：解决登录域名问题）

### CORS 配置
- `STORE_CORS`: 允许访问商店 API 的前端域名
- `ADMIN_CORS`: 允许访问管理 API 的域名
- `AUTH_CORS`: 允许访问认证 API 的域名

### 数据库配置
- `DATABASE_URL`: PostgreSQL 数据库连接字符串
- `REDIS_URL`: Redis 缓存连接字符串

### 第三方服务
- `SANITY_*`: Sanity CMS 配置
- `STRIPE_API_KEY`: Stripe 支付配置
- `RESEND_API_KEY`: 邮件服务配置

## 🚀 应用配置后的效果

1. **管理后台登录**: 现在会正确请求 `http://localhost:9000/auth/user/emailpass`
2. **API 调用**: 所有 API 请求都指向本地开发环境
3. **CORS**: 正确配置跨域访问权限
4. **第三方集成**: Sanity 和 Stripe 正常工作

## 🔐 登录信息

**管理后台**: http://localhost:9000/app/login
- 邮箱: `<EMAIL>`
- 密码: `admin123`

## 📝 注意事项

1. **重启服务**: 修改环境变量后需要重启后端服务
2. **安全性**: 生产环境中请使用更安全的密钥
3. **域名配置**: 部署到生产环境时需要更新所有 URL 配置
4. **API 密钥**: 确保第三方服务的 API 密钥有效且权限正确
