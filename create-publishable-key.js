const { Client } = require('pg');

// 数据库连接配置
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'medusa_dev',
  user: process.env.USER || 'lilsnake',
});

// 生成唯一ID的函数
function generateId(prefix = '') {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}${random}`;
}

// 生成 publishable key
function generatePublishableKey() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = 'pk_';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成 salt
function generateSalt() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成 redacted token
function generateRedacted(token) {
  return token.substring(0, 7) + '****' + token.substring(token.length - 4);
}

async function createPublishableKey() {
  try {
    await client.connect();
    console.log('🔌 已连接到数据库');

    // 1. 获取默认销售渠道
    const salesChannelResult = await client.query(`
      SELECT id, name FROM sales_channel WHERE deleted_at IS NULL LIMIT 1
    `);

    if (salesChannelResult.rows.length === 0) {
      console.error('❌ 没有找到销售渠道，请先创建销售渠道');
      return;
    }

    const salesChannel = salesChannelResult.rows[0];
    console.log(`📊 找到销售渠道: ${salesChannel.name} (${salesChannel.id})`);

    // 2. 检查是否已有 publishable key
    const existingKeys = await client.query(`
      SELECT id, title, token FROM api_key WHERE type = 'publishable'
    `);

    if (existingKeys.rows.length > 0) {
      console.log('✅ 已存在 Publishable Key:');
      const existingKey = existingKeys.rows[0];
      console.log(`  ID: ${existingKey.id}`);
      console.log(`  Title: ${existingKey.title}`);
      console.log(`  Token: ${existingKey.token}`);

      // 检查是否已关联销售渠道
      const relationCheck = await client.query(`
        SELECT * FROM publishable_api_key_sales_channel
        WHERE publishable_key_id = $1
      `, [existingKey.id]);

      if (relationCheck.rows.length === 0) {
        console.log('🔗 关联销售渠道...');
        const relationId = generateId('paksc');
        await client.query(`
          INSERT INTO publishable_api_key_sales_channel (
            id, publishable_key_id, sales_channel_id, created_at, updated_at
          ) VALUES ($1, $2, $3, NOW(), NOW())
        `, [relationId, existingKey.id, salesChannel.id]);
        console.log(`✅ 已关联销售渠道: ${salesChannel.name}`);
      }

      // 更新环境变量
      const fs = require('fs');
      const envPath = './storefront/.env';

      try {
        let envContent = fs.readFileSync(envPath, 'utf8');

        if (envContent.includes('NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=')) {
          envContent = envContent.replace(
            /NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=.*/,
            `NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${existingKey.token}`
          );
        } else {
          envContent += `\nNEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${existingKey.token}\n`;
        }

        fs.writeFileSync(envPath, envContent);
        console.log('📝 已更新 storefront/.env 文件');
      } catch (error) {
        console.warn('⚠️  无法自动更新 .env 文件:', error.message);
        console.log('📝 请手动添加以下环境变量到 storefront/.env:');
        console.log(`NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${existingKey.token}`);
      }

      return;
    }

    // 3. 如果没有现有的 key，创建新的

    // 3. 创建 publishable API key
    const keyId = generateId('pk');
    const token = generatePublishableKey();
    const salt = generateSalt();
    const redacted = generateRedacted(token);
    const title = 'Wood Carving Store Key';

    await client.query(`
      INSERT INTO api_key (
        id, token, salt, redacted, title, type, created_by, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
    `, [keyId, token, salt, redacted, title, 'publishable', 'system']);

    console.log('🔑 创建 Publishable API Key:');
    console.log(`  ID: ${keyId}`);
    console.log(`  Title: ${title}`);
    console.log(`  Token: ${token}`);

    // 4. 关联销售渠道
    const relationId = generateId('paksc');
    await client.query(`
      INSERT INTO publishable_api_key_sales_channel (
        id, publishable_key_id, sales_channel_id, created_at, updated_at
      ) VALUES ($1, $2, $3, NOW(), NOW())
    `, [relationId, keyId, salesChannel.id]);

    console.log(`✅ 已关联销售渠道: ${salesChannel.name}`);

    // 5. 更新前端环境变量文件
    const fs = require('fs');
    const envPath = './storefront/.env';
    
    try {
      let envContent = fs.readFileSync(envPath, 'utf8');
      
      // 更新或添加 NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY
      if (envContent.includes('NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=')) {
        envContent = envContent.replace(
          /# NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=.*/,
          `NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${token}`
        );
      } else {
        envContent = envContent.replace(
          /# NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=.*/,
          `NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${token}`
        );
      }
      
      fs.writeFileSync(envPath, envContent);
      console.log('📝 已更新 storefront/.env 文件');
    } catch (error) {
      console.warn('⚠️  无法自动更新 .env 文件:', error.message);
      console.log('📝 请手动添加以下环境变量到 storefront/.env:');
      console.log(`NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=${token}`);
    }

    console.log('\n🎉 Publishable Key 创建完成！');
    console.log('\n📋 下一步:');
    console.log('1. 重启前端服务: cd storefront && npm run dev');
    console.log('2. 访问: http://localhost:3000/us');

  } catch (error) {
    console.error('❌ 创建 Publishable Key 时出错:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行脚本
createPublishableKey();
