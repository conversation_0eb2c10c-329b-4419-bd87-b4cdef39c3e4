const { createClient } = require('@sanity/client');
const { config } = require('dotenv');

// 加载 storefront 目录的环境变量
config({ path: './storefront/.env' });

// 从环境变量中获取 Sanity 配置
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || 'qqvccmla',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2025-08-15',
  token: process.env.SANITY_API_TOKEN,
  useCdn: false,
});

console.log('🔧 Sanity 配置:');
console.log('  Project ID:', process.env.NEXT_PUBLIC_SANITY_PROJECT_ID);
console.log('  Dataset:', process.env.NEXT_PUBLIC_SANITY_DATASET);
console.log('  API Version:', process.env.NEXT_PUBLIC_SANITY_API_VERSION);
console.log('  Token:', process.env.SANITY_API_TOKEN ? '已设置' : '未设置');
console.log('');

async function createInitialData() {
  try {
    console.log('🚀 开始创建 Sanity 初始数据...');

    // 1. 创建 Home 文档
    console.log('📄 创建 Home 文档...');
    const homeDoc = {
      _type: 'home',
      _id: 'home',
      title: 'Wood Carving - 精美木雕艺术',
      pathname: {
        _type: 'slug',
        current: '/'
      },
      indexable: true,
      seo: {
        _type: 'seo',
        title: 'Wood Carving - 精美木雕艺术品',
        description: '探索我们精美的手工木雕艺术品，包括传统雕刻、现代木艺和实用木器。每件作品都是匠心独运的艺术珍品。',
      },
      sections: [
        {
          _type: 'section.hero',
          _key: 'hero1',
          mediaType: 'image',
          title: '精美木雕艺术',
          subtitle: '传承千年工艺，融合现代美学',
          cta: {
            _type: 'cta',
            title: '探索产品',
            href: '/products',
            variant: 'primary'
          },
          image: {
            _type: 'image',
            alt: '精美木雕艺术品展示'
          }
        }
      ]
    };

    await client.createOrReplace(homeDoc);
    console.log('  ✅ Home 文档创建成功');

    // 2. 创建 Header 文档
    console.log('📄 创建 Header 文档...');
    const headerDoc = {
      _type: 'header',
      _id: 'header',
      title: 'Wood Carving',
      navigation: [
        {
          _type: 'link',
          _key: 'nav1',
          title: '首页',
          href: '/',
        },
        {
          _type: 'link',
          _key: 'nav2', 
          title: '产品',
          href: '/products',
        },
        {
          _type: 'link',
          _key: 'nav3',
          title: '关于我们',
          href: '/about-us',
        }
      ]
    };

    await client.createOrReplace(headerDoc);
    console.log('  ✅ Header 文档创建成功');

    // 3. 创建 Footer 文档
    console.log('📄 创建 Footer 文档...');
    const footerDoc = {
      _type: 'footer',
      _id: 'footer',
      copyright: '© 2024 Wood Carving. 保留所有权利。',
      links: [
        {
          _type: 'link',
          _key: 'footer1',
          title: '隐私政策',
          href: '/privacy',
        },
        {
          _type: 'link',
          _key: 'footer2',
          title: '服务条款',
          href: '/terms',
        }
      ]
    };

    await client.createOrReplace(footerDoc);
    console.log('  ✅ Footer 文档创建成功');

    // 4. 创建 Settings 文档
    console.log('📄 创建 Settings 文档...');
    const settingsDoc = {
      _type: 'settings',
      _id: 'settings',
      title: 'Wood Carving 网站设置',
      fallbackOgImage: {
        _type: 'image',
        alt: 'Wood Carving 默认图片'
      }
    };

    await client.createOrReplace(settingsDoc);
    console.log('  ✅ Settings 文档创建成功');

    // 5. 创建 Not Found 页面
    console.log('📄 创建 Not Found 文档...');
    const notFoundDoc = {
      _type: 'not.found',
      _id: 'not-found',
      title: '页面未找到',
      pathname: {
        _type: 'slug',
        current: '/not-found'
      },
      indexable: false,
      description: '抱歉，您访问的页面不存在。',
    };

    await client.createOrReplace(notFoundDoc);
    console.log('  ✅ Not Found 文档创建成功');

    // 6. 创建 Dictionary 文档（如果需要多语言支持）
    console.log('📄 创建 Dictionary 文档...');
    const dictionaryDoc = {
      _type: 'dictionary',
      _id: 'dictionary',
      title: '词典',
      entries: []
    };

    await client.createOrReplace(dictionaryDoc);
    console.log('  ✅ Dictionary 文档创建成功');

    console.log('\n🎉 Sanity 初始数据创建完成！');
    console.log('\n📋 创建的文档：');
    console.log('  📄 Home - 主页 (/)');
    console.log('  📄 Header - 网站头部');
    console.log('  📄 Footer - 网站底部');
    console.log('  📄 Settings - 网站设置');
    console.log('  📄 Not Found - 404页面');
    console.log('  📄 Dictionary - 词典');

    console.log('\n🔍 现在可以访问:');
    console.log('  前端: http://localhost:3000/us');
    console.log('  CMS: http://localhost:3000/cms');

  } catch (error) {
    console.error('❌ 创建初始数据时出错:', error);
    
    if (error.message.includes('Insufficient permissions')) {
      console.log('\n💡 解决方案:');
      console.log('1. 确保设置了正确的 SANITY_API_TOKEN 环境变量');
      console.log('2. Token 需要有写入权限');
      console.log('3. 或者手动在 Sanity Studio 中创建这些文档');
    }
  }
}

// 运行脚本
createInitialData();
