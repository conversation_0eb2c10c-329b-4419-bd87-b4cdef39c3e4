const { Client } = require('pg');

// 数据库连接配置
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'medusa_dev',
  user: process.env.USER || 'lilsnake',
});

// 生成唯一ID的函数
function generateId(prefix = '') {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}${random}`;
}

// 木雕产品数据
const woodCarvingData = {
  // 分类数据（层级结构）
  categories: [
    {
      id: generateId('pcat'),
      name: '木雕艺术品',
      handle: 'wood-carvings',
      description: '精美的手工木雕艺术品',
      parent_category_id: null,
      is_active: true,
      is_internal: false
    },
    {
      id: generateId('pcat'),
      name: '动物雕刻',
      handle: 'animal-carvings',
      description: '各种动物主题的木雕作品',
      parent_category_id: null, // 将在插入后更新
      is_active: true,
      is_internal: false
    },
    {
      id: generateId('pcat'),
      name: '装饰雕刻',
      handle: 'decorative-carvings',
      description: '家居装饰用木雕作品',
      parent_category_id: null, // 将在插入后更新
      is_active: true,
      is_internal: false
    },
    {
      id: generateId('pcat'),
      name: '实用雕刻',
      handle: 'functional-carvings',
      description: '兼具实用性的木雕作品',
      parent_category_id: null, // 将在插入后更新
      is_active: true,
      is_internal: false
    },
    {
      id: generateId('pcat'),
      name: '雕刻工具',
      handle: 'carving-tools',
      description: '专业的木雕工具和材料',
      parent_category_id: null,
      is_active: true,
      is_internal: false
    }
  ],

  // 集合数据（营销导向）
  collections: [
    {
      id: generateId('pcol'),
      title: '新品上市',
      handle: 'new-arrivals',
      metadata: { featured: true, sort_order: 1 }
    },
    {
      id: generateId('pcol'),
      title: '热销商品',
      handle: 'best-sellers',
      metadata: { featured: true, sort_order: 2 }
    },
    {
      id: generateId('pcol'),
      title: '限量收藏',
      handle: 'limited-edition',
      metadata: { featured: true, sort_order: 3 }
    },
    {
      id: generateId('pcol'),
      title: '节日礼品',
      handle: 'holiday-gifts',
      metadata: { seasonal: true, sort_order: 4 }
    },
    {
      id: generateId('pcol'),
      title: '入门级作品',
      handle: 'beginner-friendly',
      metadata: { price_range: 'low', sort_order: 5 }
    },
    {
      id: generateId('pcol'),
      title: '大师作品',
      handle: 'master-pieces',
      metadata: { price_range: 'high', sort_order: 6 }
    }
  ],

  // 产品数据
  products: [
    // 动物雕刻系列
    {
      id: generateId('prod'),
      title: '威武老虎雕刻',
      handle: 'majestic-tiger-carving',
      description: '采用优质红木手工雕刻的威武老虎，栩栩如生，寓意威武吉祥。',
      status: 'published',
      thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      weight: '2500',
      length: '30',
      height: '20',
      width: '15',
      material: '红木',
      origin_country: 'CN',
      collection_id: null, // 将设置为热销商品
      category: 'animal-carvings'
    },
    {
      id: generateId('prod'),
      title: '温顺小象雕刻',
      handle: 'gentle-elephant-carving',
      description: '精美的小象木雕，象征着智慧和好运，适合家居装饰。',
      status: 'published',
      thumbnail: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400',
      weight: '1800',
      length: '25',
      height: '18',
      width: '12',
      material: '檀木',
      origin_country: 'CN',
      collection_id: null, // 将设置为新品上市
      category: 'animal-carvings'
    },
    {
      id: generateId('prod'),
      title: '飞翔雄鹰雕刻',
      handle: 'soaring-eagle-carving',
      description: '展翅高飞的雄鹰，雕工精细，寓意志向高远。',
      status: 'published',
      thumbnail: 'https://images.unsplash.com/photo-1574781330855-d0db8cc6a79c?w=400',
      weight: '2200',
      length: '35',
      height: '25',
      width: '20',
      material: '黄花梨',
      origin_country: 'CN',
      collection_id: null, // 将设置为限量收藏
      category: 'animal-carvings'
    },

    // 装饰雕刻系列
    {
      id: generateId('prod'),
      title: '荷花莲蓬摆件',
      handle: 'lotus-decorative-piece',
      description: '精雕细琢的荷花莲蓬，清雅脱俗，为居室增添自然气息。',
      status: 'published',
      thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      weight: '800',
      length: '20',
      height: '15',
      width: '20',
      material: '椴木',
      origin_country: 'CN',
      collection_id: null, // 将设置为节日礼品
      category: 'decorative-carvings'
    },
    {
      id: generateId('prod'),
      title: '山水风景屏风',
      handle: 'landscape-screen-carving',
      description: '传统山水题材的木雕屏风，工艺精湛，意境深远。',
      status: 'published',
      thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      weight: '5000',
      length: '80',
      height: '60',
      width: '3',
      material: '紫檀',
      origin_country: 'CN',
      collection_id: null, // 将设置为大师作品
      category: 'decorative-carvings'
    },

    // 实用雕刻系列
    {
      id: generateId('prod'),
      title: '龙纹笔筒',
      handle: 'dragon-pattern-pen-holder',
      description: '雕刻精美龙纹的实用笔筒，既实用又具有收藏价值。',
      status: 'published',
      thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      weight: '600',
      length: '12',
      height: '15',
      width: '12',
      material: '楠木',
      origin_country: 'CN',
      collection_id: null, // 将设置为入门级作品
      category: 'functional-carvings'
    },
    {
      id: generateId('prod'),
      title: '花鸟茶盘',
      handle: 'floral-tea-tray',
      description: '雕刻花鸟图案的茶盘，实用美观，品茶必备。',
      status: 'published',
      thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      weight: '1200',
      length: '40',
      height: '5',
      width: '25',
      material: '乌木',
      origin_country: 'CN',
      collection_id: null, // 将设置为热销商品
      category: 'functional-carvings'
    },

    // 雕刻工具系列
    {
      id: generateId('prod'),
      title: '专业雕刻刀套装',
      handle: 'professional-carving-knife-set',
      description: '专业木雕师使用的雕刻刀套装，包含12把不同规格的刀具。',
      status: 'published',
      thumbnail: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      weight: '800',
      length: '25',
      height: '5',
      width: '15',
      material: '高碳钢',
      origin_country: 'CN',
      collection_id: null, // 将设置为入门级作品
      category: 'carving-tools'
    }
  ]
};

async function createWoodCarvingData() {
  try {
    await client.connect();
    console.log('🔌 已连接到数据库');

    // 1. 创建分类
    console.log('📁 创建产品分类...');
    const categoryIds = {};

    for (const category of woodCarvingData.categories) {
      // mpath 字段用于存储分类的层级路径，对于根分类就是自己的ID
      const mpath = category.id;

      await client.query(`
        INSERT INTO product_category (id, name, handle, description, mpath, is_active, is_internal, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [category.id, category.name, category.handle, category.description, mpath, category.is_active, category.is_internal]);

      categoryIds[category.handle] = category.id;
      console.log(`  ✅ 创建分类: ${category.name}`);
    }

    // 2. 设置分类层级关系
    console.log('🔗 设置分类层级关系...');
    const mainCategoryId = categoryIds['wood-carvings'];

    // 更新子分类的父分类和 mpath
    const childCategories = ['animal-carvings', 'decorative-carvings', 'functional-carvings'];
    for (const childHandle of childCategories) {
      const childId = categoryIds[childHandle];
      const childMpath = `${mainCategoryId}.${childId}`;

      await client.query(`
        UPDATE product_category
        SET parent_category_id = $1, mpath = $2, updated_at = NOW()
        WHERE id = $3
      `, [mainCategoryId, childMpath, childId]);

      console.log(`  ✅ 设置 ${childHandle} 为 wood-carvings 的子分类`);
    }

    // 3. 创建集合
    console.log('📦 创建产品集合...');
    const collectionIds = {};
    
    for (const collection of woodCarvingData.collections) {
      await client.query(`
        INSERT INTO product_collection (id, title, handle, metadata, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [collection.id, collection.title, collection.handle, JSON.stringify(collection.metadata)]);
      
      collectionIds[collection.handle] = collection.id;
      console.log(`  ✅ 创建集合: ${collection.title}`);
    }

    // 4. 创建产品
    console.log('🎨 创建木雕产品...');
    const productIds = [];
    
    for (const product of woodCarvingData.products) {
      // 设置集合ID
      let collectionId = null;
      if (product.title.includes('老虎') || product.title.includes('茶盘')) {
        collectionId = collectionIds['best-sellers'];
      } else if (product.title.includes('小象')) {
        collectionId = collectionIds['new-arrivals'];
      } else if (product.title.includes('雄鹰')) {
        collectionId = collectionIds['limited-edition'];
      } else if (product.title.includes('荷花')) {
        collectionId = collectionIds['holiday-gifts'];
      } else if (product.title.includes('屏风')) {
        collectionId = collectionIds['master-pieces'];
      } else {
        collectionId = collectionIds['beginner-friendly'];
      }

      await client.query(`
        INSERT INTO product (
          id, title, handle, description, status, thumbnail, weight, length, height, width, 
          material, origin_country, collection_id, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [
        product.id, product.title, product.handle, product.description, product.status,
        product.thumbnail, product.weight, product.length, product.height, product.width,
        product.material, product.origin_country, collectionId
      ]);

      productIds.push({ id: product.id, category: product.category });
      console.log(`  ✅ 创建产品: ${product.title}`);
    }

    // 5. 建立产品与分类的关系
    console.log('🔗 建立产品分类关系...');
    for (const product of productIds) {
      const categoryId = categoryIds[product.category];
      if (categoryId) {
        await client.query(`
          INSERT INTO product_category_product (product_id, product_category_id)
          VALUES ($1, $2)
          ON CONFLICT (product_id, product_category_id) DO NOTHING
        `, [product.id, categoryId]);
      }
    }

    // 6. 创建产品变体（简单的默认变体）
    console.log('🔄 创建产品变体...');
    for (const product of woodCarvingData.products) {
      const variantId = generateId('variant');
      await client.query(`
        INSERT INTO product_variant (
          id, title, product_id, sku, manage_inventory, allow_backorder, 
          created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        ON CONFLICT (id) DO NOTHING
      `, [variantId, 'Default', product.id, product.handle.toUpperCase(), false, false]);

      console.log(`  ✅ 创建变体: ${product.title} - Default`);
    }

    console.log('\n🎉 木雕产品数据创建完成！');
    console.log('\n📊 数据统计:');
    console.log(`  📁 分类: ${woodCarvingData.categories.length} 个`);
    console.log(`  📦 集合: ${woodCarvingData.collections.length} 个`);
    console.log(`  🎨 产品: ${woodCarvingData.products.length} 个`);
    
    console.log('\n🔍 可以通过以下方式查看数据:');
    console.log('  管理后台: http://localhost:9000/app/login');
    console.log('  数据库查询: ./database-tools.sh');

  } catch (error) {
    console.error('❌ 创建数据时出错:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行脚本
createWoodCarvingData();
