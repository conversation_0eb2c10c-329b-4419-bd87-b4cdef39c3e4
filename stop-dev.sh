#!/bin/bash

echo "🛑 停止开发服务..."

# 停止后端
if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    kill $BACKEND_PID 2>/dev/null && echo "✅ 后端服务已停止"
    rm .backend.pid
fi

# 停止前端
if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    kill $FRONTEND_PID 2>/dev/null && echo "✅ 前端服务已停止"
    rm .frontend.pid
fi

# 停止可能的其他进程
pkill -f "yarn dev" 2>/dev/null
pkill -f "pnpm dev" 2>/dev/null
pkill -f "next dev" 2>/dev/null
pkill -f "medusa develop" 2>/dev/null

echo "🏁 所有服务已停止"
