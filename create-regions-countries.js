const { Client } = require('pg');

// 数据库连接配置
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'medusa_dev',
  user: process.env.USER || 'lilsnake',
});

// 生成唯一ID的函数
function generateId(prefix = '') {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}${random}`;
}

// 区域和国家数据
const regionsData = [
  {
    name: 'North America',
    currency_code: 'USD',
    countries: [
      { iso_2: 'us', iso_3: 'usa', num_code: 840, name: 'UNITED STATES', display_name: 'United States' },
      { iso_2: 'ca', iso_3: 'can', num_code: 124, name: 'CANADA', display_name: 'Canada' },
    ]
  },
  {
    name: 'Europe',
    currency_code: 'EUR',
    countries: [
      { iso_2: 'de', iso_3: 'deu', num_code: 276, name: 'GERMANY', display_name: 'Germany' },
      { iso_2: 'fr', iso_3: 'fra', num_code: 250, name: 'FRANCE', display_name: 'France' },
      { iso_2: 'gb', iso_3: 'gbr', num_code: 826, name: 'UNITED KINGDOM', display_name: 'United Kingdom' },
    ]
  },
  {
    name: 'Asia Pacific',
    currency_code: 'CNY',
    countries: [
      { iso_2: 'cn', iso_3: 'chn', num_code: 156, name: 'CHINA', display_name: 'China' },
      { iso_2: 'jp', iso_3: 'jpn', num_code: 392, name: 'JAPAN', display_name: 'Japan' },
      { iso_2: 'kr', iso_3: 'kor', num_code: 410, name: 'KOREA, REPUBLIC OF', display_name: 'South Korea' },
    ]
  }
];

async function createRegionsAndCountries() {
  try {
    await client.connect();
    console.log('🔌 已连接到数据库');

    // 1. 检查是否已有区域数据
    const existingRegions = await client.query('SELECT id, name FROM region');
    if (existingRegions.rows.length > 0) {
      console.log('✅ 已存在区域数据:');
      existingRegions.rows.forEach(region => {
        console.log(`  - ${region.name} (${region.id})`);
      });
      return;
    }

    // 2. 获取默认销售渠道
    const salesChannelResult = await client.query(`
      SELECT id, name FROM sales_channel WHERE deleted_at IS NULL LIMIT 1
    `);

    if (salesChannelResult.rows.length === 0) {
      console.error('❌ 没有找到销售渠道，请先创建销售渠道');
      return;
    }

    const salesChannel = salesChannelResult.rows[0];
    console.log(`📊 找到销售渠道: ${salesChannel.name} (${salesChannel.id})`);

    // 3. 创建区域和国家
    for (const regionData of regionsData) {
      const regionId = generateId('reg');
      
      console.log(`🌍 创建区域: ${regionData.name}`);
      
      // 创建区域
      await client.query(`
        INSERT INTO region (
          id, name, currency_code, automatic_taxes, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, NOW(), NOW())
      `, [regionId, regionData.name, regionData.currency_code, true]);

      // 区域创建完成，不需要直接关联销售渠道

      // 更新国家的区域关联
      for (const countryData of regionData.countries) {
        await client.query(`
          UPDATE region_country
          SET region_id = $1, updated_at = NOW()
          WHERE iso_2 = $2
        `, [regionId, countryData.iso_2]);

        console.log(`  ✅ 关联国家到区域: ${countryData.display_name} (${countryData.iso_2}) → ${regionData.name}`);
      }
    }

    // 4. 创建默认配送选项
    console.log('🚚 创建配送选项...');
    
    const regions = await client.query('SELECT id, name FROM region');
    for (const region of regions.rows) {
      const shippingOptionId = generateId('so');
      
      await client.query(`
        INSERT INTO shipping_option (
          id, name, region_id, profile_id, provider_id, price_type, amount, is_return, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
      `, [
        shippingOptionId,
        `Standard Shipping - ${region.name}`,
        region.id,
        'sp_01K2NTC2VRYP1HZARNTTCNEXQC', // 默认配送配置文件
        'manual_manual', // 手动配送提供商
        'flat_rate',
        1000, // $10.00
        false
      ]);

      console.log(`  ✅ 创建配送选项: Standard Shipping - ${region.name}`);
    }

    console.log('\n🎉 区域和国家数据创建完成！');
    console.log('\n📊 数据统计:');
    console.log(`  🌍 区域: ${regionsData.length} 个`);
    console.log(`  🏳️ 国家: ${regionsData.reduce((sum, region) => sum + region.countries.length, 0)} 个`);
    
    console.log('\n🔍 现在可以测试:');
    console.log('  前端: http://localhost:3000/us');
    console.log('  国家选择器应该正常工作');

  } catch (error) {
    console.error('❌ 创建区域和国家数据时出错:', error);
  } finally {
    await client.end();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行脚本
createRegionsAndCountries();
