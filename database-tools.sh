#!/bin/bash

echo "🗄️ Wood Caving 数据库管理工具"
echo "================================"

# 添加 PostgreSQL 到 PATH
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

show_menu() {
    echo ""
    echo "请选择操作："
    echo "1. 查看 PostgreSQL 状态"
    echo "2. 连接到 PostgreSQL 数据库"
    echo "3. 查看数据库表"
    echo "4. 查看 Redis 状态"
    echo "5. 连接到 Redis"
    echo "6. 打开 Redis Insight (图形化界面)"
    echo "7. 安装 pgAdmin (PostgreSQL 图形化界面)"
    echo "8. 数据库备份"
    echo "9. 退出"
    echo ""
}

check_postgresql() {
    echo "📊 PostgreSQL 状态检查..."
    if brew services list | grep postgresql@15 | grep started > /dev/null; then
        echo "✅ PostgreSQL 正在运行"
        psql -d medusa_dev -c "\conninfo"
    else
        echo "❌ PostgreSQL 未运行"
        echo "启动命令: brew services start postgresql@15"
    fi
}

check_redis() {
    echo "📊 Redis 状态检查..."
    if brew services list | grep redis | grep started > /dev/null; then
        echo "✅ Redis 正在运行"
        redis-cli ping
        echo "内存使用: $(redis-cli info memory | grep used_memory_human)"
        echo "键数量: $(redis-cli dbsize)"
    else
        echo "❌ Redis 未运行"
        echo "启动命令: brew services start redis"
    fi
}

connect_postgresql() {
    echo "🔌 连接到 PostgreSQL..."
    echo "数据库: medusa_dev"
    echo "用户: $(whoami)"
    echo "输入 \\q 退出"
    psql -d medusa_dev
}

show_tables() {
    echo "📋 数据库表列表..."
    psql -d medusa_dev -c "\dt" | head -20
    echo ""
    echo "显示前20个表，完整列表请使用选项2连接数据库后执行 \\dt"
}

connect_redis() {
    echo "🔌 连接到 Redis..."
    echo "输入 quit 退出"
    redis-cli
}

open_redis_insight() {
    echo "🖥️ 打开 Redis Insight..."
    open -a "Redis Insight"
    echo "Redis Insight 已启动！"
    echo "连接信息："
    echo "  主机: localhost"
    echo "  端口: 6379"
    echo "  无需密码"
}

install_pgadmin() {
    echo "📥 安装 pgAdmin..."
    echo "正在从官网下载 pgAdmin..."
    open "https://www.pgadmin.org/download/pgadmin-4-macos/"
    echo "请从浏览器下载并安装 pgAdmin"
    echo ""
    echo "🔧 pgAdmin 连接配置："
    echo "================================"
    echo "General 标签页:"
    echo "  Name: Wood Caving Local"
    echo ""
    echo "Connection 标签页:"
    echo "  Host name/address: localhost"
    echo "  Port: 5432"
    echo "  Maintenance database: postgres"
    echo "  Username: $(whoami)"
    echo "  Password: (留空)"
    echo ""
    echo "⚠️  重要提示："
    echo "1. Maintenance database 必须设置为 'postgres'，不是 'medusa_dev'"
    echo "2. 连接成功后，你会在左侧看到 'medusa_dev' 数据库"
    echo "3. 展开 medusa_dev > Schemas > public > Tables 查看所有表"
}

backup_database() {
    echo "💾 数据库备份..."
    BACKUP_FILE="medusa_dev_backup_$(date +%Y%m%d_%H%M%S).sql"
    pg_dump medusa_dev > "$BACKUP_FILE"
    echo "✅ 数据库已备份到: $BACKUP_FILE"
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (1-9): " choice
    
    case $choice in
        1) check_postgresql ;;
        2) connect_postgresql ;;
        3) show_tables ;;
        4) check_redis ;;
        5) connect_redis ;;
        6) open_redis_insight ;;
        7) install_pgadmin ;;
        8) backup_database ;;
        9) echo "👋 再见！"; exit 0 ;;
        *) echo "❌ 无效选项，请重新选择" ;;
    esac
    
    echo ""
    read -p "按 Enter 继续..."
done
