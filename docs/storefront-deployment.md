# 前端部署指南 (Vercel)

本文档详细介绍如何将 Next.js 前端应用部署到 Vercel，包括环境变量配置、域名设置和性能优化。

## 目录
- [前置要求](#前置要求)
- [Vercel 项目设置](#vercel-项目设置)
- [环境变量配置](#环境变量配置)
- [构建配置](#构建配置)
- [域名和 SSL](#域名和-ssl)
- [性能优化](#性能优化)
- [监控和分析](#监控和分析)
- [故障排除](#故障排除)

## 前置要求

1. **Vercel 账户**
   - 访问 [vercel.com](https://vercel.com) 注册账户
   - 连接 GitHub/GitLab/Bitbucket 账户

2. **项目准备**
   - Medusa 后端已部署并可访问
   - Sanity CMS 已配置
   - 所有第三方服务已设置

3. **本地测试**
   - 确保项目在本地正常运行
   - 所有环境变量已配置
   - 构建过程无错误

## Vercel 项目设置

### 1. 导入项目

#### 方法 A: 通过 Vercel 控制台

1. **登录 Vercel 控制台**
   - 访问 [vercel.com/dashboard](https://vercel.com/dashboard)

2. **导入项目**
   ```
   1. 点击 "New Project"
   2. 选择 Git 仓库
   3. 选择项目仓库
   4. 配置项目设置
   ```

3. **项目配置**
   ```
   Framework Preset: Next.js
   Root Directory: storefront (如果是 monorepo)
   Build Command: npm run build
   Output Directory: .next
   Install Command: npm install
   ```

#### 方法 B: 通过 Vercel CLI

```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 在项目目录中部署
cd storefront
vercel

# 生产部署
vercel --prod
```

### 2. 项目配置文件

确保项目根目录有正确的配置文件：

#### `next.config.mjs`
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        port: '',
        pathname: '/images/**',
      },
      {
        protocol: 'https',
        hostname: '*.amazonaws.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  
  // 启用实验性功能
  experimental: {
    serverComponentsExternalPackages: ['@sanity/client'],
  },
  
  // 重定向配置
  async redirects() {
    return [
      {
        source: '/admin',
        destination: '/cms',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
```

#### `vercel.json` (可选)
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "installCommand": "npm install",
  "framework": "nextjs",
  "regions": ["iad1"],
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "crons": [
    {
      "path": "/api/revalidate",
      "schedule": "0 */6 * * *"
    }
  ]
}
```

## 环境变量配置

### 1. 在 Vercel 控制台配置

1. **进入项目设置**
   - 在 Vercel 控制台选择项目
   - 点击 "Settings" → "Environment Variables"

2. **添加环境变量**

#### Medusa 配置
```bash
NEXT_PUBLIC_MEDUSA_BACKEND_URL=https://your-backend.medusajs.app/store
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_your_publishable_key_here
```

#### Sanity 配置
```bash
NEXT_PUBLIC_SANITY_PROJECT_ID=qqvccmla
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_API_VERSION=2025-08-12
SANITY_API_TOKEN=sk_your_sanity_token_here
SANITY_REVALIDATE_SECRET=your_revalidate_secret
```

#### Stripe 配置
```bash
NEXT_PUBLIC_STRIPE_KEY=pk_test_or_live_key_here
```

#### 其他配置
```bash
NODE_ENV=production
VERCEL_ENV=production
```

### 2. 环境变量管理

#### 按环境配置
```
Production: 生产环境变量
Preview: 预览环境变量（用于 PR）
Development: 开发环境变量
```

#### 批量导入
```bash
# 使用 Vercel CLI 批量导入
vercel env add NEXT_PUBLIC_MEDUSA_BACKEND_URL production
vercel env add NEXT_PUBLIC_SANITY_PROJECT_ID production
```

## 构建配置

### 1. 构建优化

#### `package.json` 脚本
```json
{
  "scripts": {
    "build": "next build",
    "start": "next start",
    "dev": "next dev",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  }
}
```

#### 构建时检查
```javascript
// next.config.mjs
const nextConfig = {
  // 类型检查
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // ESLint 检查
  eslint: {
    ignoreDuringBuilds: false,
  },
  
  // 严格模式
  reactStrictMode: true,
};
```

### 2. 依赖优化

#### 生产依赖
```json
{
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "@medusajs/js-sdk": "^2.0.0",
    "@sanity/client": "^6.0.0",
    "next-sanity": "^7.0.0"
  }
}
```

#### 开发依赖分离
```json
{
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/react": "^18.0.0",
    "typescript": "^5.0.0",
    "eslint": "^8.0.0",
    "tailwindcss": "^3.0.0"
  }
}
```

## 域名和 SSL

### 1. 自定义域名设置

1. **添加域名**
   ```
   1. 在 Vercel 项目设置中点击 "Domains"
   2. 输入自定义域名
   3. 配置 DNS 记录
   ```

2. **DNS 配置**
   ```
   类型: CNAME
   名称: www (或 @)
   值: cname.vercel-dns.com
   ```

3. **域名验证**
   ```bash
   # 检查 DNS 传播
   dig your-domain.com
   
   # 检查 SSL 证书
   curl -I https://your-domain.com
   ```

### 2. SSL 证书

Vercel 自动提供 SSL 证书：
- 自动续期
- 支持通配符证书
- Let's Encrypt 证书

### 3. 重定向配置

```javascript
// next.config.mjs
async redirects() {
  return [
    {
      source: '/:path*',
      has: [
        {
          type: 'host',
          value: 'www.your-domain.com',
        },
      ],
      destination: 'https://your-domain.com/:path*',
      permanent: true,
    },
  ];
}
```

## 性能优化

### 1. 图片优化

```javascript
// next.config.mjs
const nextConfig = {
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60,
  },
};
```

### 2. 缓存策略

```javascript
// app/layout.tsx
export const revalidate = 3600; // 1 hour

// 页面级缓存
export const dynamic = 'force-static';
export const dynamicParams = true;
```

### 3. 代码分割

```javascript
// 动态导入
const DynamicComponent = dynamic(() => import('../components/Heavy'), {
  loading: () => <p>Loading...</p>,
  ssr: false,
});

// 路由级分割
const LazyPage = lazy(() => import('./LazyPage'));
```

### 4. 字体优化

```javascript
// app/layout.tsx
import { Inter } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});
```

## 监控和分析

### 1. Vercel Analytics

```javascript
// app/layout.tsx
import { Analytics } from '@vercel/analytics/react';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  );
}
```

### 2. 性能监控

```javascript
// app/layout.tsx
import { SpeedInsights } from '@vercel/speed-insights/next';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <SpeedInsights />
      </body>
    </html>
  );
}
```

### 3. 错误监控

```javascript
// app/error.tsx
'use client';

export default function Error({ error, reset }) {
  useEffect(() => {
    // 发送错误到监控服务
    console.error(error);
  }, [error]);

  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
    </div>
  );
}
```

## 预览部署

### 1. 分支预览

Vercel 自动为每个分支创建预览部署：
```
主分支: https://your-app.vercel.app
功能分支: https://your-app-git-feature.vercel.app
PR 预览: https://your-app-git-pr-123.vercel.app
```

### 2. 预览环境配置

```bash
# 预览环境变量
NEXT_PUBLIC_MEDUSA_BACKEND_URL=https://staging-backend.medusajs.app/store
NEXT_PUBLIC_SANITY_DATASET=staging
```

### 3. 预览保护

```json
// vercel.json
{
  "github": {
    "silent": true
  },
  "functions": {
    "app/api/auth/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

## 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 检查构建日志
   vercel logs your-deployment-url
   
   # 本地测试构建
   npm run build
   ```

2. **环境变量问题**
   ```bash
   # 检查环境变量
   vercel env ls
   
   # 测试环境变量
   console.log(process.env.NEXT_PUBLIC_MEDUSA_BACKEND_URL);
   ```

3. **图片加载失败**
   ```javascript
   // 检查 next.config.mjs 中的 images 配置
   // 确保域名在 remotePatterns 中
   ```

### 调试步骤

1. **查看部署日志**
   ```bash
   vercel logs --follow
   ```

2. **检查函数日志**
   ```bash
   vercel logs --function=api/route
   ```

3. **测试 API 路由**
   ```bash
   curl https://your-app.vercel.app/api/health
   ```

## 成本优化

### Vercel 定价层级

1. **Hobby (免费)**
   - 100GB 带宽/月
   - 无自定义域名限制
   - 基础分析

2. **Pro ($20/月)**
   - 1TB 带宽/月
   - 高级分析
   - 密码保护

3. **Enterprise (联系销售)**
   - 无限带宽
   - 企业级支持
   - 高级安全功能

### 优化建议

1. **减少构建时间**
   - 使用增量构建
   - 优化依赖项
   - 缓存构建产物

2. **减少带宽使用**
   - 启用压缩
   - 优化图片
   - 使用 CDN

---

## 相关文档
- [Medusa Cloud 部署指南](./medusa-cloud-deployment.md)
- [Sanity 部署指南](./sanity-deployment.md)
- [环境变量参考](./environment-variables.md)
