# Medusa Cloud 部署指南

本文档详细介绍如何将 Medusa 后端部署到 Medusa Cloud，包括数据库、Redis 和相关服务的配置。

## 目录
- [前置要求](#前置要求)
- [Medusa Cloud 设置](#medusa-cloud-设置)
- [环境变量配置](#环境变量配置)
- [数据库配置](#数据库配置)
- [Redis 配置](#redis-配置)
- [文件存储配置](#文件存储配置)
- [支付集成](#支付集成)
- [部署流程](#部署流程)
- [部署后配置](#部署后配置)
- [故障排除](#故障排除)

## 前置要求

1. **Medusa Cloud 账户**
   - 访问 [Medusa Cloud](https://cloud.medusajs.com) 注册账户
   - 完成账户验证

2. **项目准备**
   - 确保本地项目可以正常运行
   - 准备好所有必需的环境变量
   - 确保代码已推送到 Git 仓库

3. **第三方服务账户**
   - AWS S3 账户（用于文件存储）
   - Stripe 账户（用于支付处理）
   - Sanity 账户（用于内容管理）

## Medusa Cloud 设置

### 1. 创建新项目

1. 登录 Medusa Cloud 控制台
2. 点击 "Create New Project"
3. 选择项目模板或连接现有仓库
4. 配置项目基本信息：
   ```
   项目名称: munchies-backend
   区域: 选择最近的区域（如 us-east-1）
   Git 仓库: 连接你的 GitHub/GitLab 仓库
   分支: main 或 production
   ```

### 2. 项目配置

在 Medusa Cloud 控制台中配置以下设置：

- **构建命令**: `yarn build`
- **启动命令**: `yarn start`
- **Node.js 版本**: 18.x 或更高
- **根目录**: `/backend`（如果是 monorepo）

## 环境变量配置

在 Medusa Cloud 控制台的环境变量部分添加以下配置：

### 基础配置
```bash
# 应用配置
NODE_ENV=production
JWT_SECRET=your-super-secure-jwt-secret-here
COOKIE_SECRET=your-super-secure-cookie-secret-here

# CORS 配置
STORE_CORS=https://your-storefront-domain.com
ADMIN_CORS=https://your-admin-domain.com
AUTH_CORS=https://your-admin-domain.com

# 后端 URL
BACKEND_URL=https://your-medusa-cloud-url.medusajs.app
```

### 数据库配置
```bash
# PostgreSQL 数据库
DATABASE_URL=postgresql://username:password@host:port/database
POSTGRES_URL=postgresql://username:password@host:port/database
DB_NAME=your_database_name
```

### Redis 配置
```bash
# Redis 连接
REDIS_URL=redis://username:password@host:port
```

### 文件存储配置 (AWS S3)
```bash
# S3 配置
S3_FILE_URL=https://your-bucket.s3.region.amazonaws.com
S3_ACCESS_KEY_ID=your-access-key-id
S3_SECRET_ACCESS_KEY=your-secret-access-key
S3_REGION=us-east-1
S3_BUCKET=your-bucket-name
S3_ENDPOINT=https://s3.us-east-1.amazonaws.com
```

### Sanity 集成配置
```bash
# Sanity CMS
SANITY_API_TOKEN=your-sanity-api-token
SANITY_PROJECT_ID=your-sanity-project-id
SANITY_ORGANIZATION_ID=your-sanity-org-id
```

### 支付配置
```bash
# Stripe
STRIPE_API_KEY=sk_live_your-stripe-secret-key
```

### 邮件服务配置
```bash
# Resend 邮件服务
RESEND_API_KEY=your-resend-api-key
MEDUSA_PUBLISHABLE_KEY=pk_your-medusa-publishable-key
```

## 数据库配置

### 1. 选择数据库提供商

推荐的 PostgreSQL 托管服务：

**选项 A: AWS RDS PostgreSQL**
- 高可用性和自动备份
- 与 Medusa Cloud 集成良好
- 成本: ~$20-50/月

**选项 B: DigitalOcean Managed PostgreSQL**
- 简单易用的管理界面
- 自动备份和监控
- 成本: ~$15-30/月

**选项 C: Supabase PostgreSQL**
- 免费层可用
- 实时功能支持
- 成本: $0-25/月

### 2. 数据库设置步骤

1. **创建数据库实例**
   ```sql
   -- 创建数据库
   CREATE DATABASE medusa_store;
   
   -- 创建用户（如果需要）
   CREATE USER medusa_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE medusa_store TO medusa_user;
   ```

2. **配置连接字符串**
   ```bash
   DATABASE_URL=**************************************************/medusa_store
   ```

3. **数据库迁移**
   - Medusa Cloud 会自动运行迁移
   - 确保 `medusa-config.ts` 中的数据库配置正确

## Redis 配置

### 1. 选择 Redis 提供商

推荐的 Redis 托管服务：

**选项 A: AWS ElastiCache Redis**
- 高性能和可扩展性
- 与 AWS 生态系统集成
- 成本: ~$15-40/月

**选项 B: DigitalOcean Managed Redis**
- 简单配置和管理
- 自动故障转移
- 成本: ~$15-25/月

**选项 C: Upstash Redis**
- 按使用量付费
- 全球分布式
- 成本: ~$0-20/月

### 2. Redis 设置步骤

1. **创建 Redis 实例**
   - 选择适当的内存大小（建议至少 256MB）
   - 启用持久化（如果需要）
   - 配置访问控制

2. **获取连接字符串**
   ```bash
   REDIS_URL=redis://username:password@host:port
   ```

3. **测试连接**
   ```bash
   # 使用 redis-cli 测试连接
   redis-cli -u redis://username:password@host:port ping
   ```

## 文件存储配置

### AWS S3 设置

1. **创建 S3 存储桶**
   ```bash
   # 使用 AWS CLI 创建存储桶
   aws s3 mb s3://your-medusa-files-bucket --region us-east-1
   ```

2. **配置存储桶策略**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "PublicReadGetObject",
         "Effect": "Allow",
         "Principal": "*",
         "Action": "s3:GetObject",
         "Resource": "arn:aws:s3:::your-medusa-files-bucket/*"
       }
     ]
   }
   ```

3. **创建 IAM 用户和策略**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "s3:GetObject",
           "s3:PutObject",
           "s3:DeleteObject"
         ],
         "Resource": "arn:aws:s3:::your-medusa-files-bucket/*"
       }
     ]
   }
   ```

## 支付集成

### Stripe 配置

1. **获取 API 密钥**
   - 登录 Stripe 控制台
   - 获取生产环境的密钥
   - 配置 webhook 端点

2. **Webhook 配置**
   ```
   端点 URL: https://your-medusa-cloud-url.medusajs.app/webhooks/stripe
   事件: payment_intent.succeeded, payment_intent.payment_failed
   ```

## 部署流程

### 1. 代码准备

1. **确保配置文件正确**
   ```typescript
   // medusa-config.ts 检查清单
   - ✅ 数据库 URL 使用环境变量
   - ✅ Redis URL 使用环境变量
   - ✅ CORS 配置正确
   - ✅ 所有模块配置完整
   ```

2. **推送代码到仓库**
   ```bash
   git add .
   git commit -m "准备生产部署"
   git push origin main
   ```

### 2. Medusa Cloud 部署

1. **触发部署**
   - 在 Medusa Cloud 控制台点击 "Deploy"
   - 或者推送代码到连接的分支自动触发

2. **监控部署过程**
   - 查看构建日志
   - 确认所有依赖安装成功
   - 检查启动日志

3. **验证部署**
   ```bash
   # 检查健康状态
   curl https://your-medusa-cloud-url.medusajs.app/health
   
   # 检查管理员 API
   curl https://your-medusa-cloud-url.medusajs.app/admin/auth
   ```

## 部署后配置

### 1. 创建管理员用户

```bash
# 使用 Medusa CLI 创建管理员
npx @medusajs/medusa-cli user -e <EMAIL> -p password123
```

### 2. 配置域名（可选）

1. **添加自定义域名**
   - 在 Medusa Cloud 控制台添加域名
   - 配置 DNS 记录指向 Medusa Cloud

2. **SSL 证书**
   - Medusa Cloud 自动提供 SSL 证书
   - 确认 HTTPS 访问正常

### 3. 设置监控和日志

1. **启用日志记录**
   - 在控制台查看应用日志
   - 配置日志级别和保留期

2. **设置告警**
   - 配置错误率告警
   - 设置性能监控

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库连接字符串
   # 确认数据库服务器可访问
   # 验证用户权限
   ```

2. **Redis 连接问题**
   ```bash
   # 检查 Redis URL 格式
   # 确认 Redis 服务状态
   # 验证网络连接
   ```

3. **文件上传失败**
   ```bash
   # 检查 S3 配置
   # 验证 IAM 权限
   # 确认存储桶策略
   ```

### 调试步骤

1. **查看应用日志**
   ```bash
   # 在 Medusa Cloud 控制台查看实时日志
   # 搜索错误信息和堆栈跟踪
   ```

2. **测试 API 端点**
   ```bash
   # 测试健康检查
   curl https://your-app.medusajs.app/health
   
   # 测试管理员 API
   curl https://your-app.medusajs.app/admin/auth
   ```

3. **验证环境变量**
   - 在控制台检查所有环境变量是否正确设置
   - 确认敏感信息没有泄露

## 性能优化

### 1. 数据库优化
- 配置适当的连接池大小
- 启用查询缓存
- 定期进行数据库维护

### 2. Redis 优化
- 配置适当的内存策略
- 启用持久化（如果需要）
- 监控内存使用情况

### 3. 应用优化
- 启用 gzip 压缩
- 配置适当的缓存策略
- 优化数据库查询

## 安全最佳实践

1. **环境变量安全**
   - 使用强密码和密钥
   - 定期轮换敏感信息
   - 不在代码中硬编码密钥

2. **网络安全**
   - 配置适当的 CORS 策略
   - 使用 HTTPS
   - 限制数据库访问

3. **访问控制**
   - 使用最小权限原则
   - 定期审查用户权限
   - 启用多因素认证

## 成本优化

### 预估成本（月费用）
- **Medusa Cloud**: 联系 Medusa 获取定价
- **PostgreSQL**: $15-50
- **Redis**: $15-40
- **S3 存储**: $5-20
- **总计**: $35-110+

### 优化建议
1. 选择合适的实例大小
2. 使用预留实例（如适用）
3. 定期清理不需要的资源
4. 监控使用情况并调整配置

---

## 相关文档
- [Sanity 部署指南](./sanity-deployment.md)
- [前端部署指南](./storefront-deployment.md)
- [开发环境设置](./development-setup.md)
