# Munchies 项目部署文档

本目录包含 Munchies 电商项目的完整部署指南，涵盖 Medusa 后端、Sanity CMS 和前端应用的部署配置。

## 📋 文档目录

### 核心部署指南
- **[Medusa Cloud 部署指南](./medusa-cloud-deployment.md)** - Medusa 后端、数据库、Redis 部署
- **[Sanity 部署指南](./sanity-deployment.md)** - Sanity CMS 设置和部署
- **[前端部署指南](./storefront-deployment.md)** - Next.js 前端部署到 Vercel
- **[开发环境设置](./development-setup.md)** - 本地开发环境配置

### 补充文档
- **[环境变量参考](./environment-variables.md)** - 所有环境变量的详细说明
- **[故障排除指南](./troubleshooting.md)** - 常见问题和解决方案
- **[性能优化](./performance-optimization.md)** - 生产环境性能优化建议

## 🚀 快速部署流程

### 1. 准备阶段
```bash
# 1. 克隆项目
git clone <repository-url>
cd medusa-dtc-starter-munchies

# 2. 安装依赖
pnpm install

# 3. 复制环境变量模板
cp backend/.env.template backend/.env
cp storefront/.env.template storefront/.env
```

### 2. Sanity CMS 设置
```bash
# 1. 初始化 Sanity 项目（如果需要）
cd storefront
pnpx sanity init --env

# 2. 配置环境变量
# 在 storefront/.env 中设置:
# NEXT_PUBLIC_SANITY_PROJECT_ID="your-project-id"
# NEXT_PUBLIC_SANITY_DATASET="production"
# SANITY_API_TOKEN="your-api-token"
```

### 3. Medusa 后端部署
```bash
# 1. 设置数据库和 Redis
# 推荐使用 DigitalOcean 或 AWS

# 2. 配置环境变量
# 在 backend/.env 中设置所有必需变量

# 3. 部署到 Medusa Cloud
# 在 Medusa Cloud 控制台创建项目并连接仓库
```

### 4. 前端部署
```bash
# 1. 部署到 Vercel
vercel --prod

# 2. 配置环境变量
# 在 Vercel 控制台设置所有环境变量
```

## 🏗️ 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │  Medusa Cloud   │    │   Sanity CMS    │
│   (Vercel)      │◄──►│   (Backend)     │◄──►│   (Content)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Stripe      │    │  PostgreSQL +   │    │   File Storage  │
│   (Payments)    │    │     Redis       │    │     (AWS S3)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 💰 成本估算

### 推荐配置（月费用）
| 服务 | 提供商 | 成本 |
|------|--------|------|
| 后端托管 | Medusa Cloud | 联系获取定价 |
| 数据库 | DigitalOcean PostgreSQL | $15-30 |
| 缓存 | DigitalOcean Redis | $15-25 |
| 前端托管 | Vercel Pro | $20 |
| CMS | Sanity | $0-99 |
| 文件存储 | AWS S3 | $5-20 |
| 支付处理 | Stripe | 2.9% + $0.30/交易 |
| **总计** | | **$55-194+** |

### 经济配置（月费用）
| 服务 | 提供商 | 成本 |
|------|--------|------|
| 后端托管 | Railway | $5-20 |
| 数据库 | Railway PostgreSQL | $5-15 |
| 缓存 | Railway Redis | $5-10 |
| 前端托管 | Vercel Hobby | $0 |
| CMS | Sanity | $0 |
| 文件存储 | AWS S3 | $5-20 |
| **总计** | | **$20-65** |

## 🔧 环境变量快速参考

### 后端 (backend/.env)
```bash
# 基础配置
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
JWT_SECRET=your-jwt-secret
COOKIE_SECRET=your-cookie-secret

# CORS
STORE_CORS=https://your-domain.com
ADMIN_CORS=https://your-domain.com

# 第三方服务
STRIPE_API_KEY=sk_...
SANITY_API_TOKEN=sk...
S3_ACCESS_KEY_ID=...
S3_SECRET_ACCESS_KEY=...
```

### 前端 (storefront/.env)
```bash
# Medusa
NEXT_PUBLIC_MEDUSA_BACKEND_URL=https://your-backend.medusajs.app/store
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_...

# Sanity
NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=sk...

# Stripe
NEXT_PUBLIC_STRIPE_KEY=pk_...
```

## 📋 部署检查清单

### 部署前检查
- [ ] 所有环境变量已配置
- [ ] 数据库和 Redis 实例已创建
- [ ] S3 存储桶已设置
- [ ] Stripe 账户已配置
- [ ] Sanity 项目已创建
- [ ] 域名已准备（可选）

### 部署后验证
- [ ] 后端健康检查通过
- [ ] 管理员界面可访问
- [ ] 前端页面正常加载
- [ ] Sanity Studio 可访问
- [ ] 支付流程正常
- [ ] 图片上传功能正常
- [ ] 数据同步正常

## 🔍 监控和维护

### 关键指标监控
- 应用响应时间
- 数据库连接数
- Redis 内存使用
- 错误率和异常
- 支付成功率

### 定期维护任务
- 数据库备份验证
- 安全更新应用
- 性能指标审查
- 成本优化分析
- 日志清理

## 🆘 紧急联系信息

### 服务提供商支持
- **Medusa Cloud**: [<EMAIL>](mailto:<EMAIL>)
- **Vercel**: [<EMAIL>](mailto:<EMAIL>)
- **Sanity**: [<EMAIL>](mailto:<EMAIL>)
- **DigitalOcean**: 通过控制台提交工单
- **AWS**: 通过 AWS Support Center

### 故障排除步骤
1. 检查服务状态页面
2. 查看应用日志
3. 验证环境变量
4. 测试网络连接
5. 联系相应服务支持

## 📚 相关资源

### 官方文档
- [Medusa 文档](https://docs.medusajs.com/)
- [Next.js 文档](https://nextjs.org/docs)
- [Sanity 文档](https://www.sanity.io/docs)
- [Vercel 文档](https://vercel.com/docs)

### 社区资源
- [Medusa Discord](https://discord.gg/medusajs)
- [Next.js GitHub](https://github.com/vercel/next.js)
- [Sanity Community](https://www.sanity.io/community)

---

## 📝 更新日志

### v1.0.0 (2025-08-11)
- 初始版本发布
- 包含 Medusa Cloud 和 Sanity 部署指南
- 添加成本估算和检查清单

---

**需要帮助？** 请查看具体的部署指南或联系项目维护者。
