# Sanity CMS 部署指南

本文档详细介绍如何设置和部署 Sanity CMS，包括项目配置、内容模型设置和与 Medusa 的集成。

## 目录
- [前置要求](#前置要求)
- [Sanity 项目设置](#sanity-项目设置)
- [内容模型配置](#内容模型配置)
- [环境变量配置](#环境变量配置)
- [Studio 部署](#studio-部署)
- [API 配置](#api-配置)
- [与 Medusa 集成](#与-medusa-集成)
- [内容同步](#内容同步)
- [故障排除](#故障排除)

## 前置要求

1. **Sanity 账户**
   - 访问 [Sanity.io](https://www.sanity.io) 注册账户
   - 完成邮箱验证

2. **开发环境**
   - Node.js 18+ 
   - npm/yarn/pnpm 包管理器
   - Git 版本控制

3. **项目准备**
   - 确保 Medusa 后端已配置
   - 准备好前端项目结构

## Sanity 项目设置

### 1. 创建 Sanity 项目

#### 方法 A: 使用现有项目配置

如果项目已包含 Sanity 配置，跳转到[环境变量配置](#环境变量配置)。

#### 方法 B: 初始化新项目

1. **进入前端目录**
   ```bash
   cd storefront
   ```

2. **初始化 Sanity 项目**
   ```bash
   pnpx sanity init --env
   ```

3. **按照提示配置**
   ```
   ✔ Select project to use: Create new project
   ✔ Your project name: Munchies CMS
   ✔ Use the default dataset configuration? Yes
   ✔ Project output path: ./sanity
   ✔ Select project template: Clean project with no predefined schemas
   ```

### 2. 项目配置文件

确保以下配置文件存在并正确配置：

#### `sanity.config.ts`
```typescript
import config from "@/config";
import {visionTool} from "@sanity/vision";
import {pages} from "@tinloof/sanity-studio";
import {defineConfig, isDev} from "sanity";
import {structureTool} from "sanity/structure";
import {imageHotspotArrayPlugin} from "sanity-plugin-hotspot-array";

export default defineConfig({
  basePath: config.sanity.studioUrl, // "/cms"
  dataset: config.sanity.dataset,
  projectId: config.sanity.projectId,
  
  plugins: [
    pages({
      creatablePages: ["modular.page", "text.page"],
      previewUrl: {
        previewMode: {
          enable: "/api/draft",
        },
      },
    }),
    structureTool({
      defaultDocumentNode,
      structure,
      title: "General",
    }),
    visionTool({defaultApiVersion: config.sanity.apiVersion}),
    imageHotspotArrayPlugin(),
  ],
  
  title: config.siteName,
});
```

#### `sanity.cli.ts`
```typescript
import config from "@/config";
import {defineCliConfig} from "sanity/cli";

export default defineCliConfig({
  api: {
    dataset: config.sanity.dataset,
    projectId: config.sanity.projectId
  }
});
```

## 内容模型配置

### 1. 核心内容类型

项目包含以下主要内容类型：

#### Product（产品）
```typescript
// sanity/schemas/documents/product.ts
export const product = defineType({
  name: 'product',
  title: 'Product',
  type: 'document',
  fields: [
    {
      name: 'internalTitle',
      title: 'Internal Title',
      type: 'string',
      description: '从 Medusa 同步的产品标题'
    },
    {
      name: 'pathname',
      title: 'Pathname',
      type: 'slug',
      description: '产品页面路径'
    },
    {
      name: 'seo',
      title: 'SEO',
      type: 'seo'
    },
    // 其他自定义字段...
  ]
});
```

#### Collection（集合）
```typescript
// sanity/schemas/documents/collection.ts
export const collection = defineType({
  name: 'collection',
  title: 'Collection',
  type: 'document',
  fields: [
    {
      name: 'internalTitle',
      title: 'Internal Title',
      type: 'string'
    },
    {
      name: 'pathname',
      title: 'Pathname',
      type: 'slug'
    },
    // 其他字段...
  ]
});
```

#### Category（分类）
```typescript
// sanity/schemas/documents/category.ts
export const category = defineType({
  name: 'category',
  title: 'Category',
  type: 'document',
  fields: [
    {
      name: 'internalTitle',
      title: 'Internal Title',
      type: 'string'
    },
    {
      name: 'pathname',
      title: 'Pathname',
      type: 'slug'
    },
    // 其他字段...
  ]
});
```

### 2. 页面内容类型

#### 首页配置
```typescript
// sanity/schemas/singletons/home.ts
export const home = defineType({
  name: 'home',
  title: 'Home',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string'
    },
    {
      name: 'seo',
      title: 'SEO',
      type: 'seo'
    },
    {
      name: 'sections',
      title: 'Sections',
      type: 'sectionsBody'
    }
  ]
});
```

#### 设置页面
```typescript
// sanity/schemas/singletons/settings.ts
export const settings = defineType({
  name: 'settings',
  title: 'Settings',
  type: 'document',
  fields: [
    {
      name: 'fallbackOgImage',
      title: 'Fallback sharing image',
      type: 'ogImage',
      validation: (Rule) => Rule.required(),
    },
    // 其他全局设置...
  ]
});
```

## 环境变量配置

### 1. Sanity 项目信息

在 Sanity 管理控制台获取以下信息：

1. **访问 Sanity 管理控制台**
   - 前往 [https://www.sanity.io/manage](https://www.sanity.io/manage)
   - 选择你的项目

2. **获取项目 ID**
   ```
   项目 ID: 在项目设置中找到
   例如: qqvccmla
   ```

3. **获取数据集名称**
   ```
   数据集: 通常为 "production"
   ```

4. **生成 API Token**
   - 进入 API 部分
   - 点击 "Add API token"
   - 选择权限: Editor
   - 复制生成的 token

### 2. 前端环境变量

在 `storefront/.env` 文件中配置：

```bash
## Sanity 配置
NEXT_PUBLIC_SANITY_PROJECT_ID="qqvccmla"
NEXT_PUBLIC_SANITY_DATASET="production"
SANITY_API_TOKEN="sk_your_sanity_api_token_here"
NEXT_PUBLIC_SANITY_API_VERSION="2025-08-12"  # 今天的日期 YYYY-MM-DD

## 可选配置
SANITY_REVALIDATE_SECRET="your-revalidate-secret"
```

### 3. 后端环境变量

在 `backend/.env` 文件中配置：

```bash
## Sanity 集成
SANITY_API_TOKEN="sk_your_sanity_api_token_here"
SANITY_PROJECT_ID="qqvccmla"
SANITY_ORGANIZATION_ID="your_org_id"
```

## Studio 部署

### 1. 本地开发

```bash
# 启动 Sanity Studio
cd storefront
npm run dev

# Studio 将在以下地址可用:
# http://localhost:3000/cms
```

### 2. 生产部署

#### 选项 A: 与前端一起部署（推荐）

Studio 会自动与 Next.js 应用一起部署到 Vercel：

```bash
# 部署到 Vercel
vercel --prod

# Studio 将在以下地址可用:
# https://your-domain.com/cms
```

#### 选项 B: 独立部署

```bash
# 构建 Studio
cd storefront
npx sanity build

# 部署到 Sanity 托管
npx sanity deploy
```

### 3. 域名配置

如果使用自定义域名，需要在 Sanity 项目设置中添加 CORS 源：

```
允许的源:
- http://localhost:3000
- https://your-domain.com
- https://your-preview-domain.vercel.app
```

## API 配置

### 1. API 版本设置

```typescript
// config.ts
const config = {
  sanity: {
    apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || "2025-08-12",
    // 使用今天的日期或最新的 API 版本
  }
};
```

### 2. 客户端配置

```typescript
// data/sanity/client.ts
import {createClient} from "next-sanity";
import config from "@/config";

export const client = createClient({
  apiVersion: config.sanity.apiVersion,
  dataset: config.sanity.dataset,
  projectId: config.sanity.projectId,
  useCdn: process.env.NODE_ENV === "production",
  perspective: "published",
  stega: {
    studioUrl: config.sanity.studioUrl,
  },
});
```

### 3. 图片处理配置

```typescript
// data/sanity/client.ts
import createImageUrlBuilder from "@sanity/image-url";

export const imageBuilder = createImageUrlBuilder({
  dataset: config.sanity.dataset,
  projectId: config.sanity.projectId,
});

// 使用示例
const imageUrl = imageBuilder
  .image(imageAsset)
  .width(800)
  .height(600)
  .format('webp')
  .url();
```

## 与 Medusa 集成

### 1. Medusa 端配置

在 `backend/medusa-config.ts` 中配置 Sanity 模块：

```typescript
export default defineConfig({
  modules: [
    {
      resolve: "./modules/sanity",
      options: {
        api_token: process.env.SANITY_API_TOKEN,
        project_id: process.env.SANITY_PROJECT_ID,
        api_version: new Date().toISOString().split("T")[0],
        dataset: "production",
        studio_url: "https://your-domain.com/cms",
        type_map: {
          collection: "collection",
          category: "category",
          product: "product",
        },
      },
    },
  ],
});
```

### 2. 数据同步服务

Sanity 模块会自动同步以下数据：

- **产品**: 从 Medusa 到 Sanity
- **分类**: 从 Medusa 到 Sanity  
- **集合**: 从 Medusa 到 Sanity

同步的字段包括：
```typescript
// 产品同步
{
  _id: product.id,
  _type: "product",
  internalTitle: product.title,
  pathname: { _type: "slug", current: "/products/" + product.handle }
}
```

### 3. Webhook 配置

为了实现实时同步，可以配置 webhook：

```typescript
// backend/src/api/webhooks/sanity/route.ts
export async function POST(request: Request) {
  const body = await request.json();
  
  // 处理 Sanity webhook 事件
  if (body._type === 'product') {
    // 更新产品信息
  }
  
  return new Response('OK');
}
```

## 内容同步

### 1. 初始数据导入

```bash
# 在 Medusa 后端运行种子脚本
cd backend
npm run seed

# 这会自动将产品、分类和集合同步到 Sanity
```

### 2. 实时同步

当在 Medusa 中创建、更新或删除产品时，数据会自动同步到 Sanity：

```typescript
// 自动触发的同步操作
- 产品创建 → Sanity 文档创建
- 产品更新 → Sanity 文档更新  
- 产品删除 → Sanity 文档删除
```

### 3. 手动同步

如果需要手动同步数据：

```bash
# 使用 Medusa CLI 执行同步脚本
npx medusa exec ./src/scripts/sync-sanity.ts
```

## 内容管理工作流

### 1. 产品内容管理

1. **在 Medusa 中创建产品**
   - 基本信息会自动同步到 Sanity

2. **在 Sanity 中丰富内容**
   - 添加 SEO 信息
   - 上传额外图片
   - 编写详细描述
   - 配置页面布局

3. **发布内容**
   - 在 Sanity Studio 中发布文档
   - 内容会立即在前端显示

### 2. 页面内容管理

1. **首页内容**
   - 在 Sanity Studio 中编辑 "Home" 文档
   - 配置页面区块和内容

2. **全局设置**
   - 在 "Settings" 中配置全局选项
   - 设置默认 SEO 图片

## 故障排除

### 常见问题

1. **无法连接到 Sanity 项目**
   ```bash
   # 检查环境变量
   echo $NEXT_PUBLIC_SANITY_PROJECT_ID
   echo $NEXT_PUBLIC_SANITY_DATASET
   
   # 验证 API token
   curl -H "Authorization: Bearer $SANITY_API_TOKEN" \
        "https://qqvccmla.api.sanity.io/v1/data/query/production?query=*[_type=='product'][0]"
   ```

2. **Studio 无法加载**
   ```bash
   # 检查 CORS 设置
   # 在 Sanity 管理控制台添加域名到 CORS 源
   
   # 检查配置文件
   # 确保 sanity.config.ts 中的配置正确
   ```

3. **数据同步失败**
   ```bash
   # 检查 Medusa 后端日志
   # 验证 Sanity API token 权限
   # 确认网络连接
   ```

### 调试步骤

1. **验证配置**
   ```typescript
   // 在浏览器控制台测试
   console.log(process.env.NEXT_PUBLIC_SANITY_PROJECT_ID);
   console.log(process.env.NEXT_PUBLIC_SANITY_DATASET);
   ```

2. **测试 API 连接**
   ```bash
   # 使用 Sanity CLI 测试
   npx sanity debug --secrets
   ```

3. **检查数据**
   ```bash
   # 在 Sanity Studio 中使用 Vision 查询数据
   *[_type == "product"]
   ```

## 性能优化

### 1. 图片优化

```typescript
// 使用 Next.js Image 组件
import Image from 'next/image';
import { imageBuilder } from '@/data/sanity/client';

const optimizedImageUrl = imageBuilder
  .image(image)
  .width(800)
  .height(600)
  .format('webp')
  .quality(80)
  .url();
```

### 2. 查询优化

```typescript
// 使用 GROQ 查询优化
const query = `
  *[_type == "product" && defined(pathname.current)] {
    _id,
    internalTitle,
    pathname,
    seo
  }
`;
```

### 3. 缓存策略

```typescript
// 配置适当的重新验证时间
export const revalidate = 120; // 2 分钟
```

## 安全最佳实践

1. **API Token 管理**
   - 使用最小权限原则
   - 定期轮换 API token
   - 不在客户端暴露敏感 token

2. **CORS 配置**
   - 只允许必要的域名
   - 定期审查 CORS 设置

3. **内容验证**
   - 使用 Sanity 的验证规则
   - 实施内容审核流程

---

## 相关文档
- [Medusa Cloud 部署指南](./medusa-cloud-deployment.md)
- [前端部署指南](./storefront-deployment.md)
- [开发环境设置](./development-setup.md)
