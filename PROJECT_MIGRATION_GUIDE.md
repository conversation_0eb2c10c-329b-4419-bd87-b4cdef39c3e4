# Wood Carving 项目迁移指南

本指南帮助你在新设备上快速设置 Wood Carving 项目，包括数据库备份恢复和 Sanity CMS 配置。

## 📋 目录

- [准备工作](#准备工作)
- [在当前设备上备份](#在当前设备上备份)
- [在新设备上恢复](#在新设备上恢复)
- [Sanity CMS 配置](#sanity-cms-配置)
- [故障排除](#故障排除)

## 🛠️ 准备工作

### 系统要求

- **Node.js**: 18.x 或更高版本
- **PostgreSQL**: 15.x 或更高版本
- **Git**: 最新版本
- **操作系统**: macOS, Linux, 或 Windows (WSL)

### 必要工具安装

```bash
# macOS (使用 Homebrew)
brew install node postgresql@15 git

# Ubuntu/Debian
sudo apt update
sudo apt install nodejs npm postgresql-15 git

# 安装 Yarn
npm install -g yarn

# 安装 Sanity CLI
npm install -g @sanity/cli
```

## 💾 在当前设备上备份

### 1. 备份数据库

```bash
# 在项目根目录运行
chmod +x scripts/*.sh
./scripts/backup-database.sh
```

这将创建：
- `database-backups/medusa_backup_YYYYMMDD_HHMMSS.sql.gz` - 压缩的数据库备份
- `database-backups/latest_backup.sql.gz` - 最新备份的软链接

### 2. 部署 Sanity Studio

```bash
# 部署 Sanity Studio 到远程
./scripts/deploy-sanity.sh
```

这将：
- 构建并部署 Sanity Studio 到云端
- 创建 `sanity-env-template.txt` 环境变量模板
- 提供远程 Studio 访问地址

### 3. 准备迁移文件

将以下文件复制到新设备：

```
wood-carving/
├── database-backups/
│   └── latest_backup.sql.gz
├── backend/.env
├── storefront/.env
├── sanity-env-template.txt
└── scripts/
    ├── setup-project.sh
    ├── restore-database.sh
    └── backup-database.sh
```

## 🔄 在新设备上恢复

### 1. 克隆项目

```bash
git clone <your-repo-url> wood-carving
cd wood-carving
```

### 2. 复制配置文件

将从原设备复制的文件放到对应位置：

```bash
# 复制环境变量文件
cp /path/to/backend/.env backend/
cp /path/to/storefront/.env storefront/

# 复制数据库备份
cp /path/to/database-backups/ ./

# 确保脚本可执行
chmod +x scripts/*.sh
```

### 3. 运行自动化设置

```bash
# 运行完整的项目初始化
./scripts/setup-project.sh
```

这个脚本会：
- ✅ 检查系统依赖
- 📦 安装所有项目依赖
- 🗄️ 恢复数据库备份
- 🔨 构建项目
- 📝 创建启动脚本

### 4. 启动服务

```bash
# 启动后端 (终端 1)
./start-backend.sh

# 启动前端 (终端 2)
./start-frontend.sh
```

## ☁️ Sanity CMS 配置

### 远程 Studio 访问

Sanity Studio 已部署到云端，可以直接访问：

```
https://YOUR_PROJECT_ID.sanity.studio
```

### 本地开发配置

确保 `storefront/.env` 包含正确的 Sanity 配置：

```env
NEXT_PUBLIC_SANITY_PROJECT_ID="your_project_id"
NEXT_PUBLIC_SANITY_DATASET="production"
NEXT_PUBLIC_SANITY_API_VERSION="2025-08-15"
SANITY_API_TOKEN="your_api_token"
```

### 获取 API Token

1. 访问 [Sanity 管理面板](https://www.sanity.io/manage)
2. 选择你的项目
3. 进入 "API" 标签
4. 创建新的 Token (权限: Editor)
5. 复制 Token 到环境变量

## 🚀 验证安装

### 检查服务状态

```bash
# 检查后端
curl http://localhost:9000/health

# 检查前端
curl http://localhost:3000/us

# 检查数据库
psql -d medusa_dev -c "SELECT COUNT(*) FROM product;"
```

### 访问应用

- **前端**: http://localhost:3000/us
- **管理后台**: http://localhost:9000/app
- **本地 CMS**: http://localhost:3000/cms
- **远程 CMS**: https://YOUR_PROJECT_ID.sanity.studio

### 默认登录信息

**管理后台**:
- 邮箱: `<EMAIL>`
- 密码: `admin123`

**Sanity CMS**:
- 使用你的 Sanity 账户登录

## 🔧 故障排除

### 常见问题

#### 1. PostgreSQL 连接失败

```bash
# 启动 PostgreSQL 服务
brew services start postgresql@15  # macOS
sudo systemctl start postgresql    # Linux

# 检查服务状态
pg_isready
```

#### 2. 端口被占用

```bash
# 查找占用端口的进程
lsof -i :3000  # 前端端口
lsof -i :9000  # 后端端口

# 终止进程
kill -9 <PID>
```

#### 3. 数据库恢复失败

```bash
# 手动恢复数据库
dropdb medusa_dev
createdb medusa_dev
gunzip -c database-backups/latest_backup.sql.gz | psql -d medusa_dev
```

#### 4. Sanity 权限问题

```bash
# 重新登录 Sanity
sanity auth logout
sanity auth login

# 检查项目访问权限
sanity projects list
```

### 获取帮助

如果遇到问题：

1. 检查日志输出
2. 确认环境变量配置
3. 验证网络连接
4. 查看 [Medusa 文档](https://docs.medusajs.com/)
5. 查看 [Sanity 文档](https://www.sanity.io/docs)

## 📚 额外资源

- [Medusa 开发指南](https://docs.medusajs.com/development/overview)
- [Sanity Studio 指南](https://www.sanity.io/docs/sanity-studio)
- [Next.js 文档](https://nextjs.org/docs)

---

🎉 **恭喜！** 你已经成功在新设备上设置了 Wood Carving 项目！
