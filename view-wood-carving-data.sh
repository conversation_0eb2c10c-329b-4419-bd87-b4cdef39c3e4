#!/bin/bash

echo "🎨 Wood Carving 数据查看工具"
echo "=========================="

# 设置 PostgreSQL 路径
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

show_menu() {
    echo ""
    echo "请选择查看内容："
    echo "1. 查看所有分类（层级结构）"
    echo "2. 查看所有集合"
    echo "3. 查看所有产品"
    echo "4. 查看产品与分类关系"
    echo "5. 查看产品与集合关系"
    echo "6. 查看完整的产品信息"
    echo "7. 统计数据"
    echo "8. 退出"
    echo ""
}

show_categories() {
    echo "📁 产品分类（层级结构）"
    echo "====================="
    psql -d medusa_dev -c "
        WITH RECURSIVE category_tree AS (
            -- 根分类
            SELECT id, name, handle, mpath, parent_category_id, 0 as level, name as path
            FROM product_category 
            WHERE parent_category_id IS NULL AND deleted_at IS NULL
            
            UNION ALL
            
            -- 子分类
            SELECT c.id, c.name, c.handle, c.mpath, c.parent_category_id, 
                   ct.level + 1, ct.path || ' > ' || c.name
            FROM product_category c
            JOIN category_tree ct ON c.parent_category_id = ct.id
            WHERE c.deleted_at IS NULL
        )
        SELECT 
            REPEAT('  ', level) || '📂 ' || name as \"分类结构\",
            handle as \"Handle\",
            CASE WHEN level = 0 THEN '根分类' ELSE '子分类' END as \"类型\"
        FROM category_tree 
        ORDER BY path;
    "
}

show_collections() {
    echo "📦 产品集合"
    echo "=========="
    psql -d medusa_dev -c "
        SELECT 
            '📦 ' || title as \"集合名称\",
            handle as \"Handle\",
            (metadata->>'featured')::boolean as \"首页展示\",
            (metadata->>'sort_order')::int as \"排序\"
        FROM product_collection 
        WHERE deleted_at IS NULL
        ORDER BY (metadata->>'sort_order')::int;
    "
}

show_products() {
    echo "🎨 木雕产品列表"
    echo "=============="
    psql -d medusa_dev -c "
        SELECT 
            '🎨 ' || title as \"产品名称\",
            handle as \"Handle\",
            material as \"材质\",
            length || 'x' || width || 'x' || height || 'cm' as \"尺寸\",
            weight || 'g' as \"重量\",
            status as \"状态\"
        FROM product 
        WHERE deleted_at IS NULL
        ORDER BY title;
    "
}

show_product_categories() {
    echo "🔗 产品分类关系"
    echo "=============="
    psql -d medusa_dev -c "
        SELECT 
            '🎨 ' || p.title as \"产品名称\",
            '📂 ' || pc.name as \"所属分类\",
            CASE 
                WHEN pc.parent_category_id IS NULL THEN '根分类'
                ELSE '子分类'
            END as \"分类级别\"
        FROM product p
        JOIN product_category_product pcp ON p.id = pcp.product_id
        JOIN product_category pc ON pcp.product_category_id = pc.id
        WHERE p.deleted_at IS NULL AND pc.deleted_at IS NULL
        ORDER BY pc.name, p.title;
    "
}

show_product_collections() {
    echo "📦 产品集合关系"
    echo "=============="
    psql -d medusa_dev -c "
        SELECT 
            '🎨 ' || p.title as \"产品名称\",
            '📦 ' || pc.title as \"所属集合\",
            pc.metadata->>'featured' as \"首页展示\"
        FROM product p
        JOIN product_collection pc ON p.collection_id = pc.id
        WHERE p.deleted_at IS NULL AND pc.deleted_at IS NULL
        ORDER BY pc.title, p.title;
    "
}

show_full_product_info() {
    echo "📋 完整产品信息"
    echo "=============="
    psql -d medusa_dev -c "
        SELECT 
            p.title as \"产品名称\",
            p.description as \"产品描述\",
            p.material as \"材质\",
            p.length || 'x' || p.width || 'x' || p.height || 'cm' as \"尺寸\",
            p.weight || 'g' as \"重量\",
            pc.title as \"所属集合\",
            string_agg(pcat.name, ', ') as \"所属分类\"
        FROM product p
        LEFT JOIN product_collection pc ON p.collection_id = pc.id
        LEFT JOIN product_category_product pcp ON p.id = pcp.product_id
        LEFT JOIN product_category pcat ON pcp.product_category_id = pcat.id
        WHERE p.deleted_at IS NULL
        GROUP BY p.id, p.title, p.description, p.material, p.length, p.width, p.height, p.weight, pc.title
        ORDER BY p.title;
    "
}

show_statistics() {
    echo "📊 数据统计"
    echo "=========="
    
    echo "📁 分类统计："
    psql -d medusa_dev -c "
        SELECT 
            CASE 
                WHEN parent_category_id IS NULL THEN '根分类'
                ELSE '子分类'
            END as \"分类类型\",
            COUNT(*) as \"数量\"
        FROM product_category 
        WHERE deleted_at IS NULL
        GROUP BY parent_category_id IS NULL;
    "
    
    echo ""
    echo "📦 集合统计："
    psql -d medusa_dev -c "
        SELECT 
            COUNT(*) as \"集合总数\",
            COUNT(CASE WHEN metadata->>'featured' = 'true' THEN 1 END) as \"首页展示集合\"
        FROM product_collection 
        WHERE deleted_at IS NULL;
    "
    
    echo ""
    echo "🎨 产品统计："
    psql -d medusa_dev -c "
        SELECT 
            COUNT(*) as \"产品总数\",
            COUNT(CASE WHEN status = 'published' THEN 1 END) as \"已发布产品\",
            COUNT(CASE WHEN collection_id IS NOT NULL THEN 1 END) as \"有集合的产品\"
        FROM product 
        WHERE deleted_at IS NULL;
    "
    
    echo ""
    echo "🔗 关系统计："
    psql -d medusa_dev -c "
        SELECT 
            COUNT(*) as \"产品分类关系数\"
        FROM product_category_product pcp
        JOIN product p ON pcp.product_id = p.id
        JOIN product_category pc ON pcp.product_category_id = pc.id
        WHERE p.deleted_at IS NULL AND pc.deleted_at IS NULL;
    "
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (1-8): " choice
    
    case $choice in
        1) show_categories ;;
        2) show_collections ;;
        3) show_products ;;
        4) show_product_categories ;;
        5) show_product_collections ;;
        6) show_full_product_info ;;
        7) show_statistics ;;
        8) echo "👋 再见！"; exit 0 ;;
        *) echo "❌ 无效选项，请重新选择" ;;
    esac
    
    echo ""
    read -p "按 Enter 继续..."
done
