#!/bin/bash

echo "👤 Medusa 用户管理工具"
echo "======================"

# 设置环境变量
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 20 > /dev/null 2>&1
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# 检查是否在后端目录
if [ ! -f "package.json" ] || [ ! -d "node_modules" ]; then
    echo "❌ 请在 backend 目录下运行此脚本"
    echo "运行: cd backend && ../user-management.sh"
    exit 1
fi

show_menu() {
    echo ""
    echo "请选择操作："
    echo "1. 创建新的管理员账户"
    echo "2. 创建邀请链接"
    echo "3. 查看现有用户"
    echo "4. 重置用户密码"
    echo "5. 删除用户"
    echo "6. 显示当前登录信息"
    echo "7. 退出"
    echo ""
}

create_admin_user() {
    echo "📝 创建新的管理员账户"
    echo "====================="
    
    # 获取邮箱
    while true; do
        read -p "请输入邮箱地址: " email
        if [[ "$email" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            break
        else
            echo "❌ 请输入有效的邮箱地址"
        fi
    done
    
    # 获取密码
    while true; do
        read -s -p "请输入密码 (至少6位): " password
        echo ""
        if [ ${#password} -ge 6 ]; then
            read -s -p "请再次输入密码确认: " password_confirm
            echo ""
            if [ "$password" = "$password_confirm" ]; then
                break
            else
                echo "❌ 两次输入的密码不一致，请重新输入"
            fi
        else
            echo "❌ 密码至少需要6位字符"
        fi
    done
    
    echo "🔄 正在创建用户..."
    if yarn medusa user -e "$email" -p "$password"; then
        echo "✅ 用户创建成功！"
        echo ""
        echo "🔐 登录信息："
        echo "  邮箱: $email"
        echo "  密码: $password"
        echo "  登录地址: http://localhost:9000/app/login"
    else
        echo "❌ 用户创建失败，可能邮箱已存在"
    fi
}

create_invite() {
    echo "📧 创建邀请链接"
    echo "=============="
    
    read -p "请输入邀请用户的邮箱地址: " email
    
    if [[ "$email" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        echo "🔄 正在创建邀请..."
        if yarn medusa user -e "$email" --invite; then
            echo "✅ 邀请创建成功！"
            echo "📧 请将邀请链接发送给用户"
        else
            echo "❌ 邀请创建失败"
        fi
    else
        echo "❌ 请输入有效的邮箱地址"
    fi
}

list_users() {
    echo "👥 查看现有用户"
    echo "============="
    
    echo "🔄 正在查询用户列表..."
    psql -d medusa_dev -c "
        SELECT 
            u.id,
            u.email,
            u.first_name,
            u.last_name,
            u.created_at::date as created_date,
            CASE WHEN u.deleted_at IS NULL THEN '活跃' ELSE '已删除' END as status
        FROM \"user\" u 
        WHERE u.deleted_at IS NULL
        ORDER BY u.created_at DESC;
    " 2>/dev/null || echo "❌ 无法连接到数据库"
}

reset_password() {
    echo "🔄 重置用户密码"
    echo "=============="
    
    read -p "请输入要重置密码的用户邮箱: " email
    
    while true; do
        read -s -p "请输入新密码 (至少6位): " new_password
        echo ""
        if [ ${#new_password} -ge 6 ]; then
            read -s -p "请再次输入新密码确认: " password_confirm
            echo ""
            if [ "$new_password" = "$password_confirm" ]; then
                break
            else
                echo "❌ 两次输入的密码不一致，请重新输入"
            fi
        else
            echo "❌ 密码至少需要6位字符"
        fi
    done
    
    echo "🔄 正在重置密码..."
    # 注意：这需要直接操作数据库，因为 Medusa CLI 没有重置密码功能
    echo "⚠️  密码重置功能需要通过管理后台或 API 实现"
    echo "建议：删除用户后重新创建，或通过管理后台重置"
}

delete_user() {
    echo "🗑️  删除用户"
    echo "==========="
    
    read -p "请输入要删除的用户邮箱: " email
    read -p "确认删除用户 $email? (y/N): " confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        echo "🔄 正在删除用户..."
        # 软删除用户
        psql -d medusa_dev -c "
            UPDATE \"user\" 
            SET deleted_at = NOW() 
            WHERE email = '$email' AND deleted_at IS NULL;
        " 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "✅ 用户已删除"
        else
            echo "❌ 删除失败，请检查邮箱是否正确"
        fi
    else
        echo "❌ 取消删除操作"
    fi
}

show_login_info() {
    echo "🔐 当前登录信息"
    echo "=============="
    echo ""
    echo "🌐 管理后台地址: http://localhost:9000/app/login"
    echo ""
    echo "👤 默认管理员账户:"
    echo "  邮箱: <EMAIL>"
    echo "  密码: admin123"
    echo ""
    echo "📋 其他用户账户:"
    psql -d medusa_dev -c "
        SELECT 
            '  邮箱: ' || u.email as login_info
        FROM \"user\" u 
        WHERE u.deleted_at IS NULL 
        AND u.email != '<EMAIL>'
        ORDER BY u.created_at DESC;
    " -t 2>/dev/null || echo "  (无法查询其他用户)"
    echo ""
    echo "💡 提示: 密码只有创建时显示，请妥善保管"
}

# 检查后端服务是否运行
check_backend() {
    if ! curl -s http://localhost:9000/health > /dev/null 2>&1; then
        echo "⚠️  警告: 后端服务似乎没有运行"
        echo "请确保后端服务正在运行: yarn dev"
        echo ""
    fi
}

# 主循环
check_backend

while true; do
    show_menu
    read -p "请输入选项 (1-7): " choice
    
    case $choice in
        1) create_admin_user ;;
        2) create_invite ;;
        3) list_users ;;
        4) reset_password ;;
        5) delete_user ;;
        6) show_login_info ;;
        7) echo "👋 再见！"; exit 0 ;;
        *) echo "❌ 无效选项，请重新选择" ;;
    esac
    
    echo ""
    read -p "按 Enter 继续..."
done
